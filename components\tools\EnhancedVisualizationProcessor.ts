import { LLMDataProcessingTool } from './LLMDataProcessingTool';
import { VisualizationRequestAnalyzerTool } from './VisualizationRequestAnalyzerTool';
import { VisualizationAnalyzerTool } from './VisualizationAnalyzerTool';

interface ProcessedVisualizationContent {
    request: string;
    data?: string;
    metadata: {
        type: string;
        format?: string;
        structure?: string;
    };
}

export class EnhancedVisualizationProcessor {
    private static readonly DATA_MARKERS = [
        'following data:',
        'data below:',
        'given data:',
        'use this data:',
        'data:'
    ];

    constructor(
        public readonly llmProcessor: LLMDataProcessingTool,
        public readonly requestAnalyzer: VisualizationRequestAnalyzerTool,
        public readonly visualizationAnalyzer: VisualizationAnalyzerTool
    ) {}

    private log(phase: string, message: string, data?: any) {
        //console.log(`[EnhancedVisualizationProcessor][${phase}] ${message}`);
        if (data) {
            //console.log(`[EnhancedVisualizationProcessor][${phase}] Data:`, JSON.stringify(data, null, 2));
        }
    }

    /**
     * Process user query and extract structured content for visualization
     * @param userQuery - The user's visualization request
     * @returns Processed and structured content
     */
    processContent(userQuery: string): ProcessedVisualizationContent {
        this.log('PROCESS', 'Starting content processing', { inputLength: userQuery.length });
        //console.log("Processing user query:", userQuery);

        try {
            // Split request and data
            const { request, data } = this.extractRequestAndData(userQuery);
            this.log('EXTRACT', 'Split request and data', { 
                requestLength: request.length,
                hasData: !!data 
            });

            // Detect format and structure
            const metadata = this.analyzeContent(request, data);
            this.log('ANALYZE', 'Content analysis complete', { metadata });

            return {
                request: request.trim(),
                data: data?.trim(),
                metadata
            };

        } catch (error) {
            this.log('ERROR', 'Content processing failed', { error });
            throw this.enhanceError(error);
        }
    }

    /**
     * Extract visualization request and data from user query
     * @param userQuery - User's complete query with potential data
     * @returns Separated request and data
     */
    private extractRequestAndData(userQuery: string): { 
        request: string; 
        data?: string; 
    } {
        // Find the first data marker
        const markerIndex = this.findDataMarkerIndex(userQuery);

        if (markerIndex === -1) {
            // No data marker found - entire input is the request
            return { 
                request: userQuery,
                data: undefined
            };
        }

        // Split at the marker
        const request = userQuery.substring(0, markerIndex).trim();
        const data = userQuery.substring(markerIndex).trim();

        return { request, data };
    }

    private findDataMarkerIndex(input: string): number {
        let earliestIndex = -1;

        EnhancedVisualizationProcessor.DATA_MARKERS.forEach(marker => {
            const index = input.toLowerCase().indexOf(marker);
            if (index !== -1 && (earliestIndex === -1 || index < earliestIndex)) {
                earliestIndex = index;
            }
        });

        return earliestIndex;
    }

    private analyzeContent(request: string, data?: string): {
        type: string;
        format?: string;
        structure?: string;
    } {
        const metadata: {
            type: string;
            format?: string;
            structure?: string;
        } = {
            type: 'visualization'
        };

        if (data) {
            // Determine data format
            if (data.includes('|')) {
                metadata.format = 'table';
                metadata.structure = 'delimited';
            } else if (data.includes(',')) {
                metadata.format = 'csv';
                metadata.structure = 'delimited';
            } else if (data.includes('{') || data.includes('[')) {
                metadata.format = 'json';
                metadata.structure = 'nested';
            } else {
                metadata.format = 'text';
                metadata.structure = 'unstructured';
            }
        }

        return metadata;
    }

    private enhanceError(error: any): Error {
        const baseMessage = error.message || 'Unknown error';
        const enhancedMessage = `Content processing failed: ${baseMessage}`;
        
        const enhancedError = new Error(enhancedMessage);
        if (error.stack) {
            enhancedError.stack = error.stack;
        }
        return enhancedError;
    }
}