/**
 * ContentSelector.ts
 *
 * This module implements vector-based content selection using Pinecone for similarity search
 * and Firestore for document storage. It manages the retrieval and processing of relevant
 * document chunks based on vector similarity queries across multiple namespaces.
 *
 * Key features:
 * - Vector similarity search using Pinecone
 * - Document chunk retrieval from Firestore
 * - Cross-namespace content querying
 * - Relevance-based content processing
 * - Token-aware content selection
 */

import { Pinecone } from "@pinecone-database/pinecone";
import { FirestoreStore } from "../../../lib/FirestoreStore";
import { DocumentProcessor } from "../../../lib/optimizedDocumentProcessing";
import { fetchDocumentChunksByChunkIds } from "../../../lib/fetchDocumentChunksByChunkIds";
import { TokenManagement } from '../tokenTracker/tokenManagement';
import { QueryMatch, ContentSelectionResult, Source } from '../types/shared';

/**
 * Manages content selection through vector similarity search and document processing.
 * Integrates with Pinecone for vector search and Firestore for document storage.
 */
export class ContentSelector {
  private pineconeIndex: any;
  private firestoreStore: FirestoreStore;
  private documentProcessor: DocumentProcessor;

  /**
   * Initializes content selector with user-specific storage paths
   *
   * @param userId - User identifier for scoping document storage
   */
  constructor(userId: string) {
    this.pineconeIndex = new Pinecone().Index(process.env.PINECONE_INDEX!);
    this.firestoreStore = new FirestoreStore({
      collectionPath: `users/${userId}/byteStoreCollection`
    });
    this.documentProcessor = new DocumentProcessor();
  }

  /**
   * Normalizes chunk IDs by removing duplicate segments while preserving the final identifier
   *
   * @param chunkId - Raw chunk identifier that may contain duplicates
   * @returns Cleaned chunk identifier
   */
  private cleanChunkId(chunkId: string): string {
    const parts = chunkId.split('_');
    if (parts.length > 2) {
      const uniqueParts = [...new Set(parts.slice(0, -1))];
      return `${uniqueParts.join('_')}_${parts[parts.length - 1]}`;
    }
    return chunkId;
  }

  /**
   * Queries a specific namespace in Pinecone for similar vectors
   *
   * @param namespace - Namespace to query within Pinecone
   * @param queryVector - Vector to find similarities against
   * @returns Array of matching results with metadata
   */
  private async queryNamespace(
    namespace: string,
    queryVector: number[]
  ): Promise<QueryMatch[]> {
    try {
      //console.log(`Querying namespace: ${namespace}`);
      const queryResponse = await this.pineconeIndex.namespace(namespace).query({
        vector: queryVector,
        topK: 5,  // Retrieve top 5 most similar vectors
        includeValues: true,
        includeMetadata: true,
      });

      // Clean and normalize chunk IDs in the response
      const mappedMatches = (queryResponse.matches || []).map((match: { metadata: { chunkId: { toString: () => string; }; }; id: any; }) => ({
        ...match,
        metadata: {
          ...match.metadata,
          chunkId: this.cleanChunkId(
            match.metadata?.chunkId ?
            match.metadata.chunkId.toString() :
            `${namespace}_${match.id}`
          )
        }
      }));

      mappedMatches.forEach((match: { metadata: { chunkId: string; }; }) => {
        //console.log(`  - Chunk ID from ${namespace}: ${match.metadata.chunkId}`);
      });

      return mappedMatches;
    } catch (error) {
      console.error(`Error querying namespace ${namespace}:`, error);
      return [];
    }
  }

  /**
   * Selects and processes content across multiple namespaces based on vector similarity
   *
   * @param queryVector - Vector representation of the query
   * @param namespaces - Array of namespaces to search within
   * @param tokenManager - Manager for tracking token usage
   * @returns Processed content with metadata or null if no relevant content found
   */
  public async selectContent(
    queryVector: number[],
    namespaces: string[],
    tokenManager: TokenManagement
  ): Promise<ContentSelectionResult | null> {
    try {
      // Query all specified namespaces in parallel
      const queryResults = await Promise.all(
        namespaces.map(namespace => this.queryNamespace(namespace, queryVector))
      );

      // Extract and filter metadata from all results
      const allMetadata = queryResults
        .flat()
        .map(match => match.metadata)
        .filter((metadata): metadata is NonNullable<typeof metadata> => !!metadata);

      // Get unique chunk IDs across all results
      const uniqueChunkIds = [...new Set(allMetadata.map(metadata => metadata.chunkId))];
      //console.log("Unique Chunk IDs after Pinecone filtering:", uniqueChunkIds);

      if (uniqueChunkIds.length === 0) {
        return null;
      }

      // Fetch actual document chunks from Firestore
      const documentChunks = await fetchDocumentChunksByChunkIds(
        uniqueChunkIds,
        this.firestoreStore
      );

      if (documentChunks.length === 0) {
        return null;
      }

      // Enrich chunks with vector search data
      const enrichedChunks = await Promise.all(
        documentChunks.map(async doc => {
          const matchingPineconeData = queryResults.flat().find(
            match => this.cleanChunkId(match.metadata?.chunkId!) === doc.metadata.chunk_id
          );

          return {
            ...doc,
            values: matchingPineconeData?.values,
            metadata: {
              ...doc.metadata,
              namespace: matchingPineconeData?.metadata?.namespace
            }
          };
        })
      );

      // Process and select most relevant chunks
      const processedData = await this.documentProcessor.selectRelevantChunks(
        enrichedChunks,
        queryVector,
        tokenManager.getTokenConfig()
      );

      // Transform source metadata into expected format
      const transformedSources: Source[] = processedData.metadata.sources.map(source => ({
        title: source.title,
        page: source.page,
        doc_id: source.doc_id || 'unknown',
        relevance: source.relevance
      }));

      // Return final processed content with metadata
      return {
        content: processedData.content,
        metadata: {
          sources: transformedSources,
          totalTokens: processedData.metadata.totalTokens,
          chunkCount: processedData.metadata.chunkCount,
          averageRelevance: processedData.metadata.averageRelevance,
          namespaceDistribution: processedData.metadata.namespaceDistribution
        }
      };

    } catch (error) {
      console.error("Error in content selection:", error);
      return null;
    }
  }
}