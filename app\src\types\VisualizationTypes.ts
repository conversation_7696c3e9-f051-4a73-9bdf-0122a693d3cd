// src/types/VisualizationTypes.ts

/**
 * Represents the configuration settings for a visualization chart or table.
 */
export interface VisualizationConfig {
    type: string; // e.g., 'stacked-bar', 'pie', etc.
    stacked: boolean;
    xAxis: string;
    yAxis: string;
    series: string[];
    colors: string[];
  }
  
  /**
   * Represents the metadata associated with the visualization data.
   */
  export interface VisualizationMetadata {
    type: string;
    metrics: string[];
    timeUnit?: string;
  }
  
  /**
   * Represents the content of the visualization, including configuration,
   * data, metadata, and additional properties like confidence and reasoning.
   */
  export interface VisualizationContent {
    type: 'chart' | 'table'; // Inner type indicating the visualization type
    config: VisualizationConfig;
    data: Record<string, unknown>[]; // Generic data structure for visualization
    metadata: VisualizationMetadata;
    confidence: number;
    reasoning: string;
    url?: string; // Optional URL added by SupervisorAgent
  }
  
  /**
   * Represents the overall visualization object with a top-level type and nested content.
   */
  export interface VisualizationData {
    charttype: 'visualization'; // Outer type indicating the object is a visualization
    content: VisualizationContent;
  }
  
  /**
   * Represents the output structure after processing a message.
   * It can include either a regular response or a visualization.
   */
  export interface ProcessMessageOutput {
    success: boolean;
    error?: string;
    response?: any; // Replace 'any' with the actual response type as needed
    visualization?: VisualizationData;
  }
  