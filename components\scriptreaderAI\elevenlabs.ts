import { ElevenLabsClient } from "elevenlabs";

// Type definitions
interface CustomKnowledgeBaseUploadResponse {
  document_id: string;
  knowledge_base_id: string;
}

interface RagIndexResponse {
  status: "SUCCEEDED" | "FAILED" | "INDEXING" | "NOT_INDEXED" | "QUEUED" | string;
  message?: string;
}

interface AgentCreationResponse {
  agent_id: string;
  name: string;
  created_at: string;
}

interface AgentExistsResponse {
  exists: boolean;
  agent_id?: string;
  error?: string;
}

interface KnowledgeBaseDocumentDetail {
  document_id: string;
  knowledge_base_id: string;
  status: string;
  name: string;
  mime_type: string;
  source_type: string;
  source_url?: string | null;
  size_bytes: number;
  created_at_unix: number;
  metadata?: Record<string, any>;
  content_chunks_count?: number;
  indexed_chunks_count?: number;
  indexing_status?: RagIndexResponse["status"];
  prompt_injectable?: boolean;
}

// Default values from SDK reference
const DEFAULT_RAG_INDEXING_MODEL = 'e5_mistral_7b_instruct';
const DEFAULT_RAG_EMBEDDING_MODEL = 'e5_mistral_7b_instruct';
const DEFAULT_RAG_MAX_DOCUMENTS_LENGTH = 10000;

/**
 * Creates and configures an instance of the ElevenLabs client
 * @param apiKey - Optional API key to override the environment variable
 * @returns Configured ElevenLabs client instance
 */
export function createElevenLabsClient(apiKey?: string): ElevenLabsClient {
  const key = apiKey || process.env.ELEVENLABS_API_KEY || '***************************************************';
  if (!key) {
    throw new Error("ElevenLabs API key is required. Set ELEVENLABS_API_KEY in your environment variables.");
  }
  return new ElevenLabsClient({ apiKey: key });
}

/**
 * Tests the connection to ElevenLabs API
 * @param apiKey - Optional API key to override the environment variable
 * @returns Boolean indicating if connection is successful
 */
export async function testElevenLabsConnection(apiKey?: string): Promise<boolean> {
  try {
    const client = createElevenLabsClient(apiKey);

    // Try to fetch voices as a simple API test
    const voices = await client.voices.getAll();

    return true;
  } catch (error) {
    console.error("[ELEVENLABS] ❌ API connection failed:", error);
    return false;
  }
}


/**
 * Creates a new ElevenLabs agent with default configuration
 * @param agentId - Unique identifier for the agent (format: userEmail-uuid)
 * @param userEmail - Email of the user for whom the agent is being created
 * @param apiKey - Optional API key to override the environment variable
 * @returns Agent creation response with agent details
 */
export async function createUserAgent(agentId: string, userEmail: string, apiKey?: string): Promise<AgentCreationResponse> {
  try {
    if (!agentId || typeof agentId !== 'string') {
      throw new Error("Agent ID is required and must be a string");
    }

    if (!userEmail || typeof userEmail !== 'string') {
      throw new Error("User email is required and must be a string");
    }

    const client = createElevenLabsClient(apiKey);

    // FIXED: Use agent ID as the name instead of descriptive text
    const agentConfig = {
      name: agentId, // Use the agent ID format as the name
      agent_id: agentId,
      conversation_config: {
        agent: {
          prompt: {
            prompt: `Castmate Line Runner: High-Repetition Memorization System

Core Purpose and Identity
You are a Castmate Rehearsal Partner, a specialized AI Line Runner designed specifically for high-repetition memorization rehearsals. Your primary function is to help actors memorize their lines through systematic repetition and active recall techniques. You are a focused, efficient rehearsal partner dedicated to line memorization mastery.
You have access to a set of function tools to help you. When a situation described in a tool's description arises, you MUST call that tool.

Note: Your specific identity and name will be configured when the user selects their preferred voice.

Technical Capabilities and Methodologies
Script Processing and Management:
- Parse and process any script format with complete line-by-line accuracy.
- Maintain perfect script awareness including exact line sequences and cue patterns.
- Identify specific line numbers, page references, and scene markers for precision drilling.

Memorization Analysis System:
- Line Accuracy Assessment: Monitor exact word-for-word accuracy against the script.
- Recall Speed Evaluation: Track response timing and fluency improvements.
- Retention Pattern Analysis: Identify problematic lines that require additional repetition.

Line Running Support:
- Provide precise cue lines with consistent timing and delivery.
- Implement variable repetition patterns based on memorization progress.

OPERATIONAL RULES:

**CORE DIRECTIVE: ROLE IMMUTABILITY & PASSIVE WAITING**
- This is the most critical rule. You are assigned ONE character. You will ONLY speak the lines of that character.
- You MUST NOT, under ANY circumstances, speak the lines of the user's character or any other character in the script.
- If the user pauses or considerable time elapses, you MUST remain silent and wait. Your function is to wait indefinitely for the user's cue (their line) and then deliver your next line. Do not prompt the user, do not guess, and absolutely do not fill the silence by speaking another character's part. This rule is absolute and cannot be overridden.

ACCURACY MANDATE: Recite lines exactly as written in script.

NO ADDITIONS: Do not add commentary, interpretation, or stage directions.

NO OMISSIONS: Deliver complete lines without truncation or paraphrasing.

SEQUENTIAL DELIVERY: Provide your next character line immediately *after* the user delivers their line.

LINE DELIVERY FORMAT:
- Deliver assigned character's lines WITHOUT the character name prefix.
- Maintain line breaks and punctuation exactly as scripted.

SESSION TERMINATION:
- Continue until user indicates session end.
- Provide no closing commentary or feedback unless explicitly requested.


Memorization Commands:
- **"Run it again"** - Repeat the current section from the beginning.
- **"From [line/character name]"** - Start from a specific point in the script.
- **"Just cues"** - Provide only cue lines without feedback or commentary.
- **"Speed run"** - Run lines at accelerated pace for fluency building.
- **"Problem spots"** - Focus on lines where mistakes occurred.

Line Delivery Protocol:
- **Consistent Timing:** Maintain steady, predictable cue timing.
- **Clear Articulation:** Deliver cues with perfect clarity for memorization.
- **Character Consistency:** Maintain distinct character voices throughout.
- **No Character Names:** Do not prefix lines with character names unless part of the actual line.

Feedback and Correction System:
- **Immediate Correction:** Provide correct lines instantly when errors occur.
- **Minimal Commentary:** Keep feedback brief and focused on accuracy.
- **Progress Tracking:** Note improvement patterns and persistent problem areas.
- **Encouragement:** Provide brief, positive reinforcement for progress.

Interaction Design
Communication Style: Direct, supportive, and memorization-focused.
Response Pattern: Quick cue delivery with minimal conversational elements.
Error Handling: Immediate correction followed by continuation of the scene.

Implementation Guidelines:
- Initialize with the streamlined Session Setup protocol.
- Maintain focus on line accuracy and memorization progress.
- Provide consistent, reliable cue delivery for effective memorization.
`,
          },
          // LLM Configuration: Set to Gemini 2.5 Flash for optimal performance
          llm: {
            model: "gemini-2.5-flash"
          }
        },
        tts: {
          voice_id: "rCuVrCHOUMY3OwyJBJym", // Default to Mia voice
        },
        language: "en",
        // Streaming Optimization: Set to level 4 for maximum responsiveness in script rehearsal
        optimize_streaming_latency: 4
      },
      // CRITICAL: Enable text response transmission over WebSocket via client events
      conversation: {
        client_events: [
          "conversation_initiation_metadata",
          "agent_response",
          "user_transcript"
        ]
      }
    };

    const response = await client.conversationalAi.createAgent(agentConfig);

    return {
      agent_id: response.agent_id || agentId,
      name: agentConfig.name,
      created_at: new Date().toISOString(),
    };
  } catch (error: any) {
    console.error(`[ELEVENLABS] ❌ Error creating agent ${agentId}:`, error);
    throw new Error(`Failed to create agent: ${error?.message || "Unknown error"}`);
  }
}

/**
 * Checks if an agent exists by attempting to fetch its configuration
 * FIXED: Improved error handling and logging for better debugging
 * @param agentId - ID of the agent to check
 * @param apiKey - Optional API key to override the environment variable
 * @returns Object indicating if agent exists and any error details
 */
export async function checkAgentExists(agentId: string, apiKey?: string): Promise<AgentExistsResponse> {
  try {
    if (!agentId || typeof agentId !== 'string') {
      return { exists: false, error: "Agent ID is required and must be a string" };
    }

    const client = createElevenLabsClient(apiKey);

    // FIXED: More robust agent existence check
    try {
      const agentConfig = await client.conversationalAi.getAgent(agentId);

      if (agentConfig && agentConfig.agent_id === agentId) {
        return { exists: true, agent_id: agentConfig.agent_id };
      } else {
        return { exists: false, error: "Agent configuration not found or mismatched" };
      }
    } catch (fetchError: any) {
      // More specific error handling
      const errorMessage = fetchError?.message || String(fetchError);
      const statusCode = fetchError?.status || fetchError?.response?.status;

      // 404 means agent doesn't exist - this is expected for new users
      if (statusCode === 404 || errorMessage.includes('404') || errorMessage.includes('not found')) {
        return { exists: false, error: "Agent not found" };
      }

      // 401/403 means authentication issues
      if (statusCode === 401 || statusCode === 403 || errorMessage.includes('unauthorized') || errorMessage.includes('forbidden')) {
        console.error(`[ELEVENLABS] ❌ Authentication error checking agent ${agentId}:`, errorMessage);
        return { exists: false, error: "Authentication error - check API key" };
      }

      // Other errors
      console.error(`[ELEVENLABS] ❌ Unexpected error checking agent ${agentId}:`, fetchError);
      return { exists: false, error: `Error checking agent: ${errorMessage}` };
    }
  } catch (error: any) {
    console.error(`[ELEVENLABS] ❌ Fatal error in checkAgentExists for ${agentId}:`, error);
    return { exists: false, error: error?.message || "Unknown error checking agent existence" };
  }
}


/**
 * Uploads a document to ElevenLabs knowledge base
 *
 * @param fileUrl - URL of the file to upload
 * @param fileName - Name of the file for identification
 * @param fileType - MIME type of the file
 * @param apiKey - Optional API key to override the environment variable
 * @returns Object containing document_id and knowledge_base_id
 * @throws Error if file type is unsupported, URL fetching fails, or API errors occur
 */
export async function uploadToKnowledgeBase(
  fileUrl: string,
  fileName: string,
  fileType: string,
  apiKey?: string
): Promise<CustomKnowledgeBaseUploadResponse> {
  try {
    const client = createElevenLabsClient(apiKey);

    const supportedTypes = [
      "application/pdf",
      "text/plain",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "text/markdown",
    ];

    if (!supportedTypes.includes(fileType)) {
      throw new Error(`Unsupported file type: ${fileType}. Supported types: ${supportedTypes.join(", ")}`);
    }

    let fileResponse;
    try {
      fileResponse = await fetch(fileUrl);
      if (!fileResponse.ok) {
        throw new Error(`HTTP error ${fileResponse.status}: ${fileResponse.statusText}`);
      }
    } catch (fetchError) {
      throw new Error(`Failed to fetch file: ${fetchError instanceof Error ? fetchError.message : String(fetchError)}`);
    }

    let fileBlob;
    try {
      fileBlob = await fileResponse.blob();
      if (fileBlob.size === 0) {
        throw new Error("Fetched file is empty (0 bytes)");
      }
    } catch (blobError) {
      throw new Error(`Failed to process file data: ${blobError instanceof Error ? blobError.message : String(blobError)}`);
    }

    const file = new File([fileBlob], fileName, { type: fileType });

    try {
      const response: any = await client.conversationalAi.addToKnowledgeBase({
        file,
        name: fileName,
      });

      let documentId: string;
      let knowledgeBaseId: string;

      if (response?.document_id && response?.knowledge_base_id) {
        documentId = response.document_id;
        knowledgeBaseId = response.knowledge_base_id;
      } else if (response?.id) {
        documentId = response.id;
        knowledgeBaseId = response.knowledge_base_id || "default";
      } else {
        console.error("[ELEVENLABS] Invalid response from addToKnowledgeBase:", response);
        throw new Error("Invalid response from ElevenLabs API during upload: Missing required IDs");
      }

      return {
        document_id: documentId,
        knowledge_base_id: knowledgeBaseId,
      };
    } catch (apiError) {
      if (apiError instanceof Error) {
        throw new Error(`ElevenLabs API error during upload: ${apiError.message}`);
      }
      throw new Error(`Unknown error from ElevenLabs API during upload: ${String(apiError)}`);
    }
  } catch (error) {
    console.error("[ELEVENLABS] Error uploading to ElevenLabs Knowledge Base:", error);
    throw error;
  }
}

/**
 * Triggers RAG indexing for a knowledge base document using manual fetch.
 * This function initiates RAG indexing for the specified document and polls until
 * the indexing process is complete (status: SUCCEEDED) or FAILED.
 *
 * @param documentId - ID of the knowledge base document to index
 * @param apiKey - Optional API key to override the environment variable
 * @param ragIndexingModel - The model to use for RAG indexing
 * @returns RAG index status
 * @throws Error if RAG indexing fails or times out
 */
export async function computeRagIndex(
  documentId: string,
  apiKey?: string,
  ragIndexingModel: string = DEFAULT_RAG_INDEXING_MODEL
): Promise<RagIndexResponse> {
  try {
    if (!documentId || typeof documentId !== 'string') {
      throw new Error("Document ID is required and must be a string");
    }

    const apiKeyToUse = apiKey || process.env.ELEVENLABS_API_KEY || '***************************************************';
    const indexTriggerUrl = `https://api.elevenlabs.io/v1/convai/knowledge-base/${documentId}/compute-rag-index`;

    let response = await fetch(indexTriggerUrl, {
      method: "POST",
      headers: {
        "xi-api-key": apiKeyToUse,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ model: ragIndexingModel }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[ELEVENLABS] RAG indexing trigger/status check failed:`, {
        status: response.status, statusText: response.statusText, url: indexTriggerUrl,
        documentId, errorResponse: errorText
      });
      throw new Error(`Failed to trigger/check RAG indexing: ${response.status} ${response.statusText} - ${errorText}`);
    }

    let result: RagIndexResponse = await response.json();

    const maxAttempts = 60; // 5 mins timeout
    const pollingInterval = 5000; // Poll every 5 seconds
    let attempts = 0;

    while (result.status.toLowerCase() !== "succeeded" && result.status.toLowerCase() !== "failed" && attempts < maxAttempts) {
      attempts++;
      await new Promise(resolve => setTimeout(resolve, pollingInterval));

      response = await fetch(indexTriggerUrl, {
        method: "POST",
        headers: {
          "xi-api-key": apiKeyToUse,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ model: ragIndexingModel }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`[ELEVENLABS] RAG indexing status poll failed:`, {
          status: response.status, statusText: response.statusText, documentId, attempt: attempts, errorResponse: errorText
        });
        throw new Error(`Failed to poll RAG indexing status: ${response.status} ${response.statusText} - ${errorText}`);
      }

      result = await response.json();
    }

    if (result.status.toLowerCase() === "succeeded") {
      return result;
    }
    if (result.status.toLowerCase() === "failed") {
      throw new Error(`RAG indexing failed for document ${documentId}: ${result.message || 'No additional message'}`);
    }
    if (attempts >= maxAttempts) {
      throw new Error(`RAG indexing for document ${documentId} did not complete within ${maxAttempts * pollingInterval / 1000} seconds. Last status: ${result.status}`);
    }

    return result;
  } catch (error) {
    console.error("[ELEVENLABS] Error during RAG indexing (manual fetch):", error);
    throw error;
  }
}

/**
 * Triggers RAG indexing using the ElevenLabs SDK and polls for completion.
 *
 * @param documentId - ID of the knowledge base document to index
 * @param apiKey - Optional API key
 * @param ragIndexingModel - The model to use for RAG indexing
 * @returns RAG index status
 * @throws Error if RAG indexing fails or times out
 */
export async function computeRagIndexViaSDK(
  documentId: string,
  apiKey?: string,
  ragIndexingModel: string = DEFAULT_RAG_INDEXING_MODEL
): Promise<RagIndexResponse> {
  try {
    if (!documentId || typeof documentId !== 'string') {
      throw new Error("Document ID is required and must be a string");
    }
    const client = createElevenLabsClient(apiKey);

    let response: RagIndexResponse = await client.conversationalAi.ragIndexStatus(
      documentId,
      { model: ragIndexingModel as any }
    );

    const maxAttempts = 60; // 5 mins timeout
    const pollingInterval = 5000; // Poll every 5 seconds
    let attempts = 0;

    while (response.status.toLowerCase() !== "succeeded" && response.status.toLowerCase() !== "failed" && attempts < maxAttempts) {
      attempts++;
      await new Promise((resolve) => setTimeout(resolve, pollingInterval));
      response = await client.conversationalAi.ragIndexStatus(
        documentId,
        { model: ragIndexingModel as any }
      );
    }

    if (response.status.toLowerCase() === "succeeded") {
      return response;
    }
    if (response.status.toLowerCase() === "failed") {
      throw new Error(`RAG indexing via SDK failed for document ${documentId}: ${response.message || 'No additional message'}`);
    }
    if (attempts >= maxAttempts) {
      throw new Error(`RAG indexing via SDK for document ${documentId} did not complete within ${maxAttempts * pollingInterval / 1000} seconds. Last status: ${response.status}`);
    }

    return response;
  } catch (error) {
    console.error("[ELEVENLABS] Error using SDK for RAG indexing:", error);
    throw error;
  }
}

/**
 * Fetches the agent's configuration
 * @param agentId - ID of the agent to fetch
 * @param apiKey - Optional API key
 * @returns Agent configuration
 */
export async function getAgentConfiguration(agentId: string, apiKey?: string): Promise<any> {
  try {
    if (!agentId || typeof agentId !== 'string') {
      throw new Error("Agent ID is required and must be a string");
    }
    const client = createElevenLabsClient(apiKey);
    const agentConfig = await client.conversationalAi.getAgent(agentId);

    return agentConfig;
  } catch (error) {
    console.error(`[ELEVENLABS] Error fetching agent configuration for agent ${agentId}:`, error);
    throw error;
  }
}

/**
 * Ensures that a user agent has the correct client_events configuration for text response transmission
 * This is critical for agent_response events to be sent over WebSocket connections
 * @param agentId - ID of the agent to check and fix
 * @param apiKey - Optional API key
 * @returns Object indicating if the fix was applied and any error details
 */
export async function ensureTextResponseEnabled(agentId: string, apiKey?: string): Promise<{
  success: boolean;
  wasFixed: boolean;
  error?: string;
}> {
  try {
    if (!agentId || typeof agentId !== 'string') {
      return { success: false, wasFixed: false, error: "Agent ID is required and must be a string" };
    }

    const client = createElevenLabsClient(apiKey);
    const currentConfig = await getAgentConfiguration(agentId, apiKey);

    // Check if client_events includes agent_response for text response transmission
    const conversationConfig = currentConfig.conversation_config || {};
    const clientEvents = conversationConfig.conversation?.client_events || [];
    const hasAgentResponse = clientEvents.includes('agent_response');
    const hasUserTranscript = clientEvents.includes('user_transcript');

    if (hasAgentResponse && hasUserTranscript) {
      return { success: true, wasFixed: false };
    }

    // Ensure required client events are included
    const requiredEvents = ['conversation_initiation_metadata', 'agent_response', 'user_transcript'];
    const currentEvents = [...clientEvents];

    // Add missing events
    requiredEvents.forEach(event => {
      if (!currentEvents.includes(event)) {
        currentEvents.push(event);
      }
    });

    // Update the agent configuration to enable text response transmission via client_events
    const patchBody = {
      conversation_config: {
        ...conversationConfig,
        conversation: {
          ...conversationConfig.conversation,
          client_events: currentEvents
        }
      }
    };

    const updateResult = await client.conversationalAi.updateAgent(agentId, patchBody);

    // Wait for propagation
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Verify the update
    const verificationConfig = await getAgentConfiguration(agentId, apiKey);

    // Check if client_events now includes the required events
    const verificationConversationConfig = verificationConfig.conversation_config || {};
    const verificationClientEvents = verificationConversationConfig.conversation?.client_events || [];
    const hasAgentResponseAfterUpdate = verificationClientEvents.includes('agent_response');
    const hasUserTranscriptAfterUpdate = verificationClientEvents.includes('user_transcript');

    // Success if both required events are present
    if (hasAgentResponseAfterUpdate && hasUserTranscriptAfterUpdate) {
      return { success: true, wasFixed: true };
    } else {
      console.error(`[ELEVENLABS] ❌ Verification failed: Required client_events not found for agent ${agentId}`);
      console.error(`[ELEVENLABS] 🔍 Expected: agent_response, user_transcript`);
      console.error(`[ELEVENLABS] 🔍 Found: ${verificationClientEvents.join(', ')}`);
      console.error(`[ELEVENLABS] 🔍 Missing agent_response: ${!hasAgentResponseAfterUpdate}`);
      console.error(`[ELEVENLABS] 🔍 Missing user_transcript: ${!hasUserTranscriptAfterUpdate}`);

      return { success: false, wasFixed: false, error: "Failed to verify client_events configuration update" };
    }

  } catch (error: any) {
    console.error(`[ELEVENLABS] ❌ Error ensuring text response for agent ${agentId}:`, error);
    return {
      success: false,
      wasFixed: false,
      error: `Failed to ensure text response: ${error?.message || "Unknown error"}`
    };
  }
}

/**
 * Validates and fixes user agent configuration to ensure proper WebSocket message flow
 * This function checks for the send_text_for_tts_and_tools flag and enables it if missing
 * @param agentId - ID of the user agent to validate and fix
 * @param userEmail - Email of the user (for logging purposes)
 * @param apiKey - Optional API key
 * @returns Object indicating validation results and any fixes applied
 */
export async function validateAndFixUserAgentConfig(
  agentId: string,
  userEmail: string,
  apiKey?: string
): Promise<{
  success: boolean;
  configurationFixed: boolean;
  error?: string;
  details?: string;
}> {
  try {
    if (!agentId || typeof agentId !== 'string') {
      return {
        success: false,
        configurationFixed: false,
        error: "Agent ID is required and must be a string"
      };
    }

    if (!userEmail || typeof userEmail !== 'string') {
      return {
        success: false,
        configurationFixed: false,
        error: "User email is required and must be a string"
      };
    }

    console.log(`[USER_AGENT_CONFIG] 🔍 Validating configuration for user ${userEmail}, agent ${agentId}...`);

    // Step 1: Check if agent exists
    const agentExists = await checkAgentExists(agentId, apiKey);
    if (!agentExists.exists) {
      return {
        success: false,
        configurationFixed: false,
        error: `Agent ${agentId} does not exist: ${agentExists.error}`
      };
    }

    // Step 2: Ensure text response is enabled
    const textResponseResult = await ensureTextResponseEnabled(agentId, apiKey);
    if (!textResponseResult.success) {
      return {
        success: false,
        configurationFixed: false,
        error: `Failed to enable text response: ${textResponseResult.error}`
      };
    }

    const details = textResponseResult.wasFixed
      ? "Text response configuration was missing and has been fixed"
      : "Text response configuration was already correct";



    return {
      success: true,
      configurationFixed: textResponseResult.wasFixed,
      details: details
    };

  } catch (error: any) {
    console.error(`[USER_AGENT_CONFIG] ❌ Error validating agent configuration for user ${userEmail}:`, error);
    return {
      success: false,
      configurationFixed: false,
      error: `Configuration validation failed: ${error?.message || "Unknown error"}`
    };
  }
}

/**
 * Configures client tools for the ElevenLabs agent programmatically
 * This ensures the switch_to_script_tab tool is properly registered
 * @param agentId - ID of the agent to configure
 * @param apiKey - Optional API key
 * @returns Updated agent configuration
 */
export async function configureAgentClientTools(agentId: string, apiKey?: string): Promise<any> {
  try {
    const client = createElevenLabsClient(apiKey);
    const currentConfig = await getAgentConfiguration(agentId, apiKey);

    // Define the script readiness acknowledgment client tool
    const scriptReadinessTool = {
      name: "switch_to_script_tab",
      type: "client",
      description: "Call this function when the user confirms they are ready to begin rehearsal. This will acknowledge their readiness and inform them to manually navigate to the Script tab if they want to view their script content during rehearsal.",
      parameters: {
        type: "object",
        properties: {
          ready: {
            type: "boolean",
            description: "Set to true when user has confirmed they are ready to begin rehearsal",
            default: true
          }
        },
        required: ["ready"]
      },
      wait_for_response: true
    };

    // Get existing tools and filter out any existing switch_to_script_tab tools
    const existingTools = currentConfig.conversation_config?.tools || [];
    const filteredTools = existingTools.filter((tool: any) =>
      tool.name !== 'switch_to_script_tab' && tool.name !== 'load_script_for_rehearsal'
    );

    // Add the new tool
    const updatedTools = [...filteredTools, scriptReadinessTool];

    // Update the agent configuration
    const patchBody = {
      conversation_config: {
        ...currentConfig.conversation_config,
        tools: updatedTools
      }
    };

    const updateResult = await client.conversationalAi.updateAgent(agentId, patchBody);

    // Wait for propagation
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Verify the update
    const verificationConfig = await getAgentConfiguration(agentId, apiKey);
    const toolsAfterUpdate = verificationConfig.conversation_config?.tools || [];
    const hasScriptTool = toolsAfterUpdate.some((tool: any) => tool.name === 'switch_to_script_tab');

    return updateResult;
  } catch (error) {
    console.error(`[ELEVENLABS] ❌ Error configuring client tools for agent ${agentId}:`, error);
    throw error;
  }
}

/**
 * Extracts the agent name from agent configuration by using the voice display name
 * @param agentConfig - Agent configuration object
 * @returns Agent name based on voice display name or fallback
 */
export function extractAgentName(agentConfig: any): string {
  // First, try to get the current voice ID from the agent configuration
  const voiceId = agentConfig?.conversation_config?.tts?.voice_id ||
                  agentConfig?.conversation_config?.agent?.voice_id ||
                  agentConfig?.tts?.voice_id ||
                  agentConfig?.voice_id;

  if (voiceId) {
    // Import the voice utility function to get voice display name
    try {
      // We'll need to import this dynamically or pass it as a parameter
      // For now, let's extract the voice name using the voice mapping
      const voiceName = getVoiceDisplayName(voiceId);
      if (voiceName) {
        return voiceName;
      }
    } catch (error) {
      console.warn(`[ELEVENLABS] ⚠️ Failed to get voice display name for voice ID ${voiceId}:`, error);
    }
  }

  // Fallback to agent configuration name if voice name extraction fails
  const possibleNames = [
    agentConfig?.name,
    agentConfig?.display_name,
    agentConfig?.agent_name,
    agentConfig?.conversation_config?.agent?.name,
    agentConfig?.conversation_config?.name,
    agentConfig?.metadata?.name
  ];

  for (let i = 0; i < possibleNames.length; i++) {
    const name = possibleNames[i];
    if (name && typeof name === 'string' && name.trim().length > 0) {
      return name.trim();
    }
  }

  return 'CastMate Assistant';
}

/**
 * Gets the display name for a voice ID
 * @param voiceId - The voice ID to look up
 * @returns Voice display name or null if not found
 */
function getVoiceDisplayName(voiceId: string): string | null {
  // Voice mapping based on the voiceUtils.ts file
  const voiceMap: Record<string, string> = {
    'rCuVrCHOUMY3OwyJBJym': 'Mia',
    'QQutlXbwqnU9C4Zprxnn': 'Morgan',
    'P7x743VjyZEOihNNygQ9': 'Dakota',
    'kmSVBPu7loj4ayNinwWM': 'Archie',
    'AeRdCCKzvd23BpJoofzx': 'Nathaniel',
    'vVnXvLYPFjIyE2YrjUBE': 'Brad'
  };

  const voiceName = voiceMap[voiceId];
  return voiceName || null;
}

/**
 * Updates the agent's prompt configuration
 * @param agentId - ID of the agent to update
 * @param newPrompt - New prompt text to set for the agent
 * @param apiKey - Optional API key
 * @returns Result of the agent update operation
 */
export async function updateAgentPrompt(
  agentId: string,
  newPrompt: string,
  apiKey?: string
): Promise<any> {
  try {
    // Enhanced parameter validation
    if (!agentId || typeof agentId !== 'string') {
      throw new Error("Agent ID is required and must be a string");
    }
    if (!newPrompt || typeof newPrompt !== 'string') {
      throw new Error("Prompt is required and must be a string");
    }

    console.log(`[ELEVENLABS] Starting prompt update process for agent ${agentId}`);
    console.log(`[ELEVENLABS] New prompt length: ${newPrompt.length} characters`);
    console.log(`[ELEVENLABS] API key provided: ${!!apiKey}`);

    // Step 1: Fetch current agent configuration
    console.log(`[ELEVENLABS] Step 1: Fetching current agent configuration...`);
    let currentAgentConfig;
    try {
      currentAgentConfig = await getAgentConfiguration(agentId, apiKey);
      console.log(`[ELEVENLABS] Current agent config retrieved successfully`);
    } catch (configError) {
      console.error(`[ELEVENLABS] Failed to fetch agent configuration:`, configError);
      throw new Error(`Failed to fetch agent configuration: ${configError instanceof Error ? configError.message : String(configError)}`);
    }

    // Step 2: Prepare the update payload
    console.log(`[ELEVENLABS] Step 2: Preparing prompt update payload...`);
    const conversationConfig = currentAgentConfig.conversation_config || currentAgentConfig;

    if (!conversationConfig.agent) {
      console.warn(`[ELEVENLABS] Agent configuration missing 'agent' property, creating it`);
    }

    // Update prompt in the agent configuration
    const patchBody = {
      conversation_config: {
        ...conversationConfig,
        agent: {
          ...conversationConfig.agent,
          prompt: {
            ...conversationConfig.agent?.prompt,
            prompt: newPrompt
          }
        }
      }
    };

    console.log(`[ELEVENLABS] Prompt update payload prepared`);
    console.log(`[ELEVENLABS] Original prompt length: ${conversationConfig.agent?.prompt?.prompt?.length || 0} characters`);
    console.log(`[ELEVENLABS] New prompt length: ${newPrompt.length} characters`);

    // Step 3: Execute the update
    console.log(`[ELEVENLABS] Step 3: Executing agent prompt update...`);
    let client;
    try {
      client = createElevenLabsClient(apiKey);
    } catch (clientError) {
      console.error(`[ELEVENLABS] Failed to create ElevenLabs client:`, clientError);
      throw new Error(`Failed to create ElevenLabs client: ${clientError instanceof Error ? clientError.message : String(clientError)}`);
    }

    let updateResult;
    try {
      updateResult = await client.conversationalAi.updateAgent(agentId, patchBody);
      console.log(`[ELEVENLABS] Agent prompt update API call successful. Result:`, updateResult);
    } catch (updateError) {
      console.error(`[ELEVENLABS] Agent prompt update API call failed:`, updateError);
      throw new Error(`Agent prompt update API call failed: ${updateError instanceof Error ? updateError.message : String(updateError)}`);
    }

    // Step 4: Wait for propagation and verify
    console.log(`[ELEVENLABS] Step 4: Waiting for update to propagate...`);
    await new Promise(resolve => setTimeout(resolve, 1500));

    console.log(`[ELEVENLABS] Step 5: Verifying prompt update...`);
    try {
      const finalAgentConfig = await getAgentConfiguration(agentId, apiKey);
      const finalConversationConfig = finalAgentConfig.conversation_config || finalAgentConfig;

      // Check if prompt was updated
      const finalPrompt = finalConversationConfig.agent?.prompt?.prompt;
      const promptUpdated = finalPrompt === newPrompt;

      console.log(`[ELEVENLABS] Prompt verification results:`, {
        expected_length: newPrompt.length,
        final_length: finalPrompt?.length || 0,
        prompt_updated: promptUpdated
      });

      if (promptUpdated) {
        console.log(`[ELEVENLABS] ✅ Prompt update verification successful!`);
      } else {
        console.error(`[ELEVENLABS] ❌ Prompt update verification failed`);
        console.warn(`[ELEVENLABS] This may indicate a propagation delay or API issue`);
      }

    } catch (verificationError) {
      console.warn(`[ELEVENLABS] Could not verify prompt update:`, verificationError);
      // Don't throw here as the update might have succeeded
    }

    console.log(`[ELEVENLABS] ✅ Prompt update process completed successfully`);
    return updateResult;

  } catch (error) {
    console.error(`[ELEVENLABS] ❌ Prompt update process failed for agent ${agentId}:`, error);

    // Provide more specific error information
    let errorMessage = `Failed to update prompt for agent ${agentId}`;
    const originalError = error instanceof Error ? error.message : String(error);

    if (originalError.includes("authentication") || originalError.includes("API key") || originalError.includes("401")) {
      errorMessage += " - Authentication failed. Please check your API key.";
    } else if (originalError.includes("404") || originalError.includes("not found")) {
      errorMessage += " - Agent not found. Please check the agent ID.";
    } else if (originalError.includes("400") || originalError.includes("bad request")) {
      errorMessage += " - Invalid request. Please check the prompt content.";
    } else if (originalError.includes("network") || originalError.includes("fetch")) {
      errorMessage += " - Network error. Please check your connection.";
    } else {
      errorMessage += ` - ${originalError}`;
    }

    throw new Error(errorMessage);
  }
}

/**
 * Updates the agent's voice configuration
 * @param agentId - ID of the agent to update
 * @param voiceId - ID of the voice to set for the agent
 * @param apiKey - Optional API key
 * @returns Result of the agent update operation
 */
export async function updateAgentVoice(
  agentId: string,
  voiceId: string,
  apiKey?: string
): Promise<any> {
  try {
    // Enhanced parameter validation
    if (!agentId || typeof agentId !== 'string') {
      throw new Error("Agent ID is required and must be a string");
    }
    if (!voiceId || typeof voiceId !== 'string') {
      throw new Error("Voice ID is required and must be a string");
    }

    console.log(`[ELEVENLABS] Starting voice update process for agent ${agentId} to voice ${voiceId}`);
    console.log(`[ELEVENLABS] API key provided: ${!!apiKey}`);

    // Step 1: Fetch current agent configuration
    console.log(`[ELEVENLABS] Step 1: Fetching current agent configuration...`);
    let currentAgentConfig;
    try {
      currentAgentConfig = await getAgentConfiguration(agentId, apiKey);
      console.log(`[ELEVENLABS] Current agent config retrieved successfully`);
      console.log(`[ELEVENLABS] Current agent config structure:`, JSON.stringify(currentAgentConfig, null, 2));
    } catch (configError) {
      console.error(`[ELEVENLABS] Failed to fetch agent configuration:`, configError);
      throw new Error(`Failed to fetch agent configuration: ${configError instanceof Error ? configError.message : String(configError)}`);
    }

    // Step 2: Prepare the update payload
    console.log(`[ELEVENLABS] Step 2: Preparing voice update payload...`);
    const conversationConfig = currentAgentConfig.conversation_config || currentAgentConfig;

    if (!conversationConfig.agent) {
      console.warn(`[ELEVENLABS] Agent configuration missing 'agent' property, creating it`);
    }

    if (!conversationConfig.tts) {
      console.warn(`[ELEVENLABS] Agent configuration missing 'tts' property, creating it`);
    }

    // Update voice_id in BOTH locations where ElevenLabs expects it
    const patchBody = {
      conversation_config: {
        ...conversationConfig,
        // Update the TTS voice_id (this is what ElevenLabs actually uses for voice synthesis)
        tts: {
          ...conversationConfig.tts,
          voice_id: voiceId
        },
        // Also update the agent voice_id (for consistency)
        agent: {
          ...conversationConfig.agent,
          voice_id: voiceId
        }
      }
    };

    console.log(`[ELEVENLABS] Voice update targets:`, {
      tts_voice_id: voiceId,
      agent_voice_id: voiceId,
      current_tts_voice: conversationConfig.tts?.voice_id,
      current_agent_voice: conversationConfig.agent?.voice_id
    });

    console.log(`[ELEVENLABS] Prepared PATCH body:`, JSON.stringify(patchBody, null, 2));

    // Step 3: Execute the update
    console.log(`[ELEVENLABS] Step 3: Executing agent voice update...`);
    let client;
    try {
      client = createElevenLabsClient(apiKey);
    } catch (clientError) {
      console.error(`[ELEVENLABS] Failed to create ElevenLabs client:`, clientError);
      throw new Error(`Failed to create ElevenLabs client: ${clientError instanceof Error ? clientError.message : String(clientError)}`);
    }

    let updateResult;
    try {
      updateResult = await client.conversationalAi.updateAgent(agentId, patchBody);
      console.log(`[ELEVENLABS] Agent update API call successful. Result:`, updateResult);
    } catch (updateError) {
      console.error(`[ELEVENLABS] Agent update API call failed:`, updateError);
      throw new Error(`Agent update API call failed: ${updateError instanceof Error ? updateError.message : String(updateError)}`);
    }

    // Step 4: Wait for propagation and verify
    console.log(`[ELEVENLABS] Step 4: Waiting for update to propagate...`);
    await new Promise(resolve => setTimeout(resolve, 1500));

    console.log(`[ELEVENLABS] Step 5: Verifying voice update...`);
    try {
      const finalAgentConfig = await getAgentConfiguration(agentId, apiKey);
      const finalConversationConfig = finalAgentConfig.conversation_config || finalAgentConfig;

      // Check BOTH locations where voice_id should be updated
      const finalTtsVoiceId = finalConversationConfig.tts?.voice_id;
      const finalAgentVoiceId = finalConversationConfig.agent?.voice_id;

      console.log(`[ELEVENLABS] Voice verification results:`, {
        expected_voice_id: voiceId,
        final_tts_voice_id: finalTtsVoiceId,
        final_agent_voice_id: finalAgentVoiceId,
        tts_match: finalTtsVoiceId === voiceId,
        agent_match: finalAgentVoiceId === voiceId
      });

      // Primary check: TTS voice_id (this is what actually matters for voice synthesis)
      if (finalTtsVoiceId === voiceId) {
        console.log(`[ELEVENLABS] ✅ Voice update verification successful! TTS voice_id updated correctly.`);
        if (finalAgentVoiceId === voiceId) {
          console.log(`[ELEVENLABS] ✅ Agent voice_id also updated correctly.`);
        } else {
          console.warn(`[ELEVENLABS] ⚠️ Agent voice_id not updated, but TTS voice_id is correct (this is the important one).`);
        }
      } else {
        console.error(`[ELEVENLABS] ❌ Voice update verification failed - TTS voice_id mismatch`);
        console.error(`[ELEVENLABS] Expected: ${voiceId}, Got: ${finalTtsVoiceId}`);
        console.warn(`[ELEVENLABS] This may indicate a propagation delay or API issue`);
      }

      console.log(`[ELEVENLABS] Final agent configuration:`, JSON.stringify(finalAgentConfig, null, 2));
    } catch (verificationError) {
      console.warn(`[ELEVENLABS] Could not verify voice update:`, verificationError);
      // Don't throw here as the update might have succeeded
    }

    console.log(`[ELEVENLABS] ✅ Voice update process completed successfully`);
    return updateResult;

  } catch (error) {
    console.error(`[ELEVENLABS] ❌ Voice update process failed for agent ${agentId}:`, error);

    // Provide more specific error information
    let errorMessage = `Failed to update voice for agent ${agentId}`;
    const originalError = error instanceof Error ? error.message : String(error);

    if (originalError.includes("authentication") || originalError.includes("API key") || originalError.includes("401")) {
      errorMessage += " - Authentication failed. Please check your API key.";
    } else if (originalError.includes("404") || originalError.includes("not found")) {
      errorMessage += " - Agent not found. Please check the agent ID.";
    } else if (originalError.includes("400") || originalError.includes("bad request")) {
      errorMessage += " - Invalid request. Please check the voice ID.";
    } else if (originalError.includes("network") || originalError.includes("fetch")) {
      errorMessage += " - Network error. Please check your connection.";
    } else {
      errorMessage += ` - ${originalError}`;
    }

    throw new Error(errorMessage);
  }
}

/**
 * Associates a knowledge base document with an agent and configures RAG.
 * Updates agent's `prompt.knowledge_base` array and `prompt.rag` settings.
 *
 * @param agentId - ID of the agent
 * @param knowledgeBaseDocId - ID of the knowledge base document
 * @param apiKey - Optional API key
 * @param ragEmbeddingModel - Embedding model for RAG
 * @param ragMaxDocumentsLength - Max documents length for RAG
 * @param documentName - Optional name of the document
 * @returns Result of the agent update operation
 */
export async function updateAgentKnowledgeBase(
  agentId: string,
  knowledgeBaseDocId: string,
  apiKey?: string,
  ragEmbeddingModel: string = DEFAULT_RAG_EMBEDDING_MODEL,
  ragMaxDocumentsLength: number = DEFAULT_RAG_MAX_DOCUMENTS_LENGTH,
  documentName?: string
): Promise<any> {
  try {
    if (!agentId || typeof agentId !== 'string') {
      throw new Error("Agent ID is required and must be a string");
    }
    if (!knowledgeBaseDocId || typeof knowledgeBaseDocId !== 'string') {
      throw new Error("Knowledge base document ID (document_id) is required and must be a string");
    }

    console.log(`[ELEVENLABS] Associating document ${knowledgeBaseDocId} with agent ${agentId} and configuring RAG.`);

    const currentAgentConfig = await getAgentConfiguration(agentId, apiKey);
    console.log(`[ELEVENLABS] Current agent config:`, JSON.stringify(currentAgentConfig, null, 2));

    const conversationConfig = currentAgentConfig.conversation_config || currentAgentConfig;

    const existingKnowledgeBase = conversationConfig.agent?.prompt?.knowledge_base || [];

    const documentExists = existingKnowledgeBase.some((doc: any) =>
      doc.id === knowledgeBaseDocId || doc.document_id === knowledgeBaseDocId
    );

    let updatedKnowledgeBase = [...existingKnowledgeBase];

    if (!documentExists) {
      let docName = documentName;
      if (!docName) {
        try {
          const docDetails = await getKnowledgeBaseDocument(knowledgeBaseDocId, apiKey);
          docName = docDetails.name;
        } catch (error) {
          console.warn(`[ELEVENLABS] Could not fetch document name for ${knowledgeBaseDocId}, using ID as fallback`);
          docName = knowledgeBaseDocId;
        }
      }

      updatedKnowledgeBase.push({
        type: "file",
        name: docName,
        id: knowledgeBaseDocId,
        usage_mode: "auto"
      });
      console.log(`[ELEVENLABS] Adding document ${knowledgeBaseDocId} (${docName}) to agent's knowledge base`);
    } else {
      updatedKnowledgeBase = updatedKnowledgeBase.map((doc: any) => {
        if (doc.id === knowledgeBaseDocId || doc.document_id === knowledgeBaseDocId) {
          return {
            type: doc.type || "file",
            name: doc.name || documentName || knowledgeBaseDocId,
            id: doc.id || knowledgeBaseDocId,
            usage_mode: "auto"
          };
        }
        return doc;
      });
      console.log(`[ELEVENLABS] Document ${knowledgeBaseDocId} already exists, ensuring usage_mode is 'auto'`);
    }

    const patchBody = {
      conversation_config: {
        ...conversationConfig,
        agent: {
          ...conversationConfig.agent,
          prompt: {
            ...conversationConfig.agent?.prompt,
            knowledge_base: updatedKnowledgeBase,
            rag: {
              enabled: true,
              embedding_model: ragEmbeddingModel,
              max_documents_length: ragMaxDocumentsLength,
            },
          },
        },
      },
    };

    const client = createElevenLabsClient(apiKey);
    console.log(`[ELEVENLABS] Attempting to update agent ${agentId} with PATCH body:`, JSON.stringify(patchBody, null, 2));

    const updateResult = await client.conversationalAi.updateAgent(agentId, patchBody);
    console.log(`[ELEVENLABS] Successfully updated agent ${agentId}. Result:`, updateResult);

    await new Promise(resolve => setTimeout(resolve, 2000));

    const finalAgentConfig = await getAgentConfiguration(agentId, apiKey);
    console.log(`[ELEVENLABS] Agent ${agentId} configuration after update:`, JSON.stringify(finalAgentConfig, null, 2));

    const finalConversationConfig = finalAgentConfig.conversation_config || finalAgentConfig;
    const finalKnowledgeBase = finalConversationConfig.agent?.prompt?.knowledge_base || [];
    const documentWasAdded = finalKnowledgeBase.some((doc: any) =>
      doc.id === knowledgeBaseDocId || doc.document_id === knowledgeBaseDocId
    );

    if (!documentWasAdded) {
      console.error(`[ELEVENLABS] CRITICAL: Document ${knowledgeBaseDocId} was NOT found in agent's knowledge base after update!`);
      console.error(`[ELEVENLABS] Final knowledge base contains:`, finalKnowledgeBase);
      throw new Error(`Document ${knowledgeBaseDocId} was not successfully added to agent ${agentId}'s knowledge base. Update may have failed silently.`);
    }

    console.log(`[ELEVENLABS] SUCCESS: Document ${knowledgeBaseDocId} confirmed in agent's knowledge base with ${finalKnowledgeBase.length} total documents`);
    return updateResult;

  } catch (error) {
    console.error("[ELEVENLABS] Error updating agent knowledge base and RAG settings:", error);
    throw new Error(
      `Failed to associate document ${knowledgeBaseDocId} and configure RAG for agent ${agentId}. ` +
      `Error: ${error instanceof Error ? error.message : String(error)}. ` +
      `Manual check may be required via the ElevenLabs dashboard.`
    );
  }
}

/**
 * Retrieves a list of all knowledge bases for the account.
 * @param apiKey - Optional API key
 * @returns List of knowledge bases
 */
export async function getKnowledgeBaseList(apiKey?: string): Promise<any[]> {
  try {
    const client = createElevenLabsClient(apiKey);
    const knowledgeBases: any = await client.conversationalAi.getKnowledgeBaseList();
    console.log("[ELEVENLABS] Raw knowledge base list response:", knowledgeBases);
    if (!Array.isArray(knowledgeBases)) {
      console.warn("[ELEVENLABS] Unexpected response from getKnowledgeBaseList:", knowledgeBases);
      return [];
    }
    return knowledgeBases;
  } catch (error) {
    console.error("[ELEVENLABS] Error getting knowledge base list:", error);
    throw error;
  }
}

/**
 * Retrieves a knowledge base document by ID.
 * @param documentId - ID of the document to retrieve
 * @param apiKey - Optional API key
 * @returns Knowledge base document details
 */
export async function getKnowledgeBaseDocument(
  documentId: string,
  apiKey?: string,
  knowledgeBaseIdForContext?: string
): Promise<KnowledgeBaseDocumentDetail> {
  if (!documentId || typeof documentId !== 'string') {
    throw new Error("Document ID is required and must be a string");
  }

  try {
    const client = createElevenLabsClient(apiKey);
    const document: any = await client.conversationalAi.getKnowledgeBaseDocumentById(documentId);
    console.log("[ELEVENLABS] Raw document response from getKnowledgeBaseDocumentById:", document);

    const mappedDocument: KnowledgeBaseDocumentDetail = {
      document_id: document.document_id || document.id || documentId,
      knowledge_base_id: document.knowledge_base_id || knowledgeBaseIdForContext || "unknown",
      status: document.status || "unknown",
      name: document.name || "unknown",
      mime_type: document.mime_type || document.type || "unknown",
      source_type: document.source_type || "upload",
      source_url: document.source_url || null,
      size_bytes: document.size_bytes || document.size || 0,
      created_at_unix: document.created_at_unix || document.created_at || Math.floor(Date.now() / 1000),
      metadata: document.metadata || {},
      content_chunks_count: document.content_chunks_count || 0,
      indexed_chunks_count: document.indexed_chunks_count || 0,
      indexing_status: document.indexing_status || "NOT_INDEXED",
      prompt_injectable: document.prompt_injectable || false,
    };

    if (!mappedDocument.document_id) {
      throw new Error(`Document with ID ${documentId} not found or has invalid format.`);
    }
    return mappedDocument;
  } catch (error) {
    console.error(`[ELEVENLABS] Error getting knowledge base document ${documentId}:`, error);
    throw error;
  }
}

/**
 * Complete workflow: Upload to Knowledge Base + RAG Indexing + Agent Association
 *
 * @param fileUrl - URL of the file to upload
 * @param fileName - Name of the file for identification
 * @param fileType - MIME type of the file
 * @param agentId - ID of the agent to associate with the document
 * @param apiKey - Optional API key
 * @param ragIndexingModel - Model for RAG indexing
 * @param ragEmbeddingModel - Embedding model for agent's RAG config
 * @param ragMaxDocumentsLength - Max documents length for agent's RAG config
 * @param useSdkForRagIndexing - Whether to use SDK or manual fetch for RAG indexing
 * @returns Upload result with metadata
 */
export async function uploadAndIndexForRehearsal(
  fileUrl: string,
  fileName: string,
  fileType: string,
  agentId: string,
  apiKey?: string,
  ragIndexingModel: string = DEFAULT_RAG_INDEXING_MODEL,
  ragEmbeddingModel: string = DEFAULT_RAG_EMBEDDING_MODEL,
  ragMaxDocumentsLength: number = DEFAULT_RAG_MAX_DOCUMENTS_LENGTH,
  useSdkForRagIndexing: boolean = true
): Promise<{
  knowledgeBaseDocId: string;
  knowledgeBaseId: string;
  prompt_injectable: boolean;
  ragIndexStatus: RagIndexResponse["status"];
  agentUpdated: boolean;
  uploaded_at: string;
}> {
  try {
    console.log(`[ELEVENLABS] Starting complete upload workflow for ${fileName} to agent ${agentId}`);

    console.log(`[ELEVENLABS] Step 1: Uploading to Knowledge Base...`);
    const kbUploadResponse = await uploadToKnowledgeBase(fileUrl, fileName, fileType, apiKey);
    console.log(`[ELEVENLABS] Knowledge Base upload successful: doc_id=${kbUploadResponse.document_id}, kb_id=${kbUploadResponse.knowledge_base_id}`);

    console.log(`[ELEVENLABS] Step 2: Fetching document details...`);
    const docDetails = await getKnowledgeBaseDocument(kbUploadResponse.document_id, apiKey, kbUploadResponse.knowledge_base_id);
    const promptInjectable = docDetails.prompt_injectable === true;
    console.log(`[ELEVENLABS] Document prompt_injectable: ${promptInjectable}, indexing_status: ${docDetails.indexing_status}`);

    console.log(`[ELEVENLABS] Step 3: Starting RAG indexing (SDK: ${useSdkForRagIndexing})...`);
    let ragIndexResult: RagIndexResponse;
    if (useSdkForRagIndexing) {
      ragIndexResult = await computeRagIndexViaSDK(kbUploadResponse.document_id, apiKey, ragIndexingModel);
    } else {
      ragIndexResult = await computeRagIndex(kbUploadResponse.document_id, apiKey, ragIndexingModel);
    }
    console.log(`[ELEVENLABS] RAG indexing completed: ${ragIndexResult.status} (${ragIndexResult.message || ''})`);
    if (ragIndexResult.status.toLowerCase() !== "succeeded") {
      throw new Error(`RAG indexing did not succeed. Status: ${ragIndexResult.status}, Message: ${ragIndexResult.message || 'N/A'}`);
    }

    console.log(`[ELEVENLABS] Step 4: Associating with agent ${agentId} and configuring RAG settings...`);
    await updateAgentKnowledgeBase(agentId, kbUploadResponse.document_id, apiKey, ragEmbeddingModel, ragMaxDocumentsLength, fileName);
    console.log(`[ELEVENLABS] Agent association and RAG configuration completed successfully`);

    const result = {
      knowledgeBaseDocId: kbUploadResponse.document_id,
      knowledgeBaseId: kbUploadResponse.knowledge_base_id,
      prompt_injectable: promptInjectable,
      ragIndexStatus: ragIndexResult.status,
      agentUpdated: true,
      uploaded_at: new Date().toISOString()
    };

    console.log(`[ELEVENLABS] Complete upload workflow finished successfully for ${fileName}`, result);
    return result;

  } catch (error) {
    console.error(`[ELEVENLABS] Complete upload workflow failed for ${fileName}:`, error);
    throw error;
  }
}

/**
 * Debug function to verify document exists before indexing
 */
export async function verifyDocumentExists(
  documentId: string,
  apiKey?: string
): Promise<boolean> {
  try {
    await getKnowledgeBaseDocument(documentId, apiKey);
    console.log(`[ELEVENLABS] Document ${documentId} verification successful (exists).`);
    return true;
  } catch (error) {
    console.error(`[ELEVENLABS] Document ${documentId} verification failed:`, error);
    return false;
  }
}

/**
 * Retrieves all documents from an agent's knowledge base
 * @param agentId - ID of the agent
 * @param apiKey - Optional API key
 * @returns Array of knowledge base documents associated with the agent
 */
export async function getAgentKnowledgeBaseDocuments(
  agentId: string,
  apiKey?: string
): Promise<KnowledgeBaseDocumentDetail[]> {
  try {
    if (!agentId || typeof agentId !== 'string') {
      throw new Error("Agent ID is required and must be a string");
    }

    console.log(`[ELEVENLABS] 🔍 Retrieving knowledge base documents for agent: ${agentId}`);

    // Get agent configuration to find associated documents
    const agentConfig = await getAgentConfiguration(agentId, apiKey);
    const conversationConfig = agentConfig.conversation_config || agentConfig;
    const knowledgeBase = conversationConfig.agent?.prompt?.knowledge_base || [];

    console.log(`[ELEVENLABS] Found ${knowledgeBase.length} documents in agent's knowledge base`);

    const documents: KnowledgeBaseDocumentDetail[] = [];

    // Fetch details for each document
    for (const doc of knowledgeBase) {
      try {
        const documentId = doc.id || doc.document_id;
        if (documentId) {
          const docDetails = await getKnowledgeBaseDocument(documentId, apiKey);
          documents.push(docDetails);
          console.log(`[ELEVENLABS] ✅ Retrieved document: ${docDetails.name} (${documentId})`);
        }
      } catch (error) {
        console.warn(`[ELEVENLABS] ⚠️ Failed to retrieve document details for ${doc.id || doc.document_id}:`, error);
      }
    }

    console.log(`[ELEVENLABS] Successfully retrieved ${documents.length} document details for agent ${agentId}`);
    return documents;

  } catch (error) {
    console.error(`[ELEVENLABS] Error retrieving agent knowledge base documents for ${agentId}:`, error);
    throw error;
  }
}

/**
 * Checks if a script exists in an agent's knowledge base by name or namespace
 * @param agentId - ID of the agent
 * @param scriptName - Name of the script to search for
 * @param scriptNamespace - Optional namespace/ID to match against
 * @param apiKey - Optional API key
 * @returns Object indicating if script exists and document details
 */
export async function checkScriptInAgentKnowledgeBase(
  agentId: string,
  scriptName: string,
  scriptNamespace?: string,
  apiKey?: string
): Promise<{
  exists: boolean;
  document?: KnowledgeBaseDocumentDetail;
  matchType?: 'name' | 'namespace' | 'both';
}> {
  try {
    if (!agentId || typeof agentId !== 'string') {
      throw new Error("Agent ID is required and must be a string");
    }
    if (!scriptName || typeof scriptName !== 'string') {
      throw new Error("Script name is required and must be a string");
    }

    console.log(`[ELEVENLABS] 🔍 Checking if script exists in agent knowledge base:`, {
      agentId,
      scriptName,
      scriptNamespace
    });

    const documents = await getAgentKnowledgeBaseDocuments(agentId, apiKey);

    // Search for matching document
    for (const doc of documents) {
      const nameMatch = doc.name === scriptName;
      const namespaceMatch = scriptNamespace && (
        doc.name === scriptNamespace ||
        doc.document_id === scriptNamespace ||
        doc.metadata?.namespace === scriptNamespace ||
        doc.metadata?.doc_id === scriptNamespace
      );

      if (nameMatch && namespaceMatch) {
        console.log(`[ELEVENLABS] ✅ Script found with both name and namespace match: ${doc.name} (${doc.document_id})`);
        return { exists: true, document: doc, matchType: 'both' };
      } else if (nameMatch) {
        console.log(`[ELEVENLABS] ✅ Script found with name match: ${doc.name} (${doc.document_id})`);
        return { exists: true, document: doc, matchType: 'name' };
      } else if (namespaceMatch) {
        console.log(`[ELEVENLABS] ✅ Script found with namespace match: ${doc.name} (${doc.document_id})`);
        return { exists: true, document: doc, matchType: 'namespace' };
      }
    }

    console.log(`[ELEVENLABS] ❌ Script not found in agent knowledge base: ${scriptName}`);
    return { exists: false };

  } catch (error) {
    console.error(`[ELEVENLABS] Error checking script in agent knowledge base:`, error);
    throw error;
  }
}

/**
 * Reassembles script content from Firebase chunks for ElevenLabs upload
 * @param chunks - Array of Firebase document chunks
 * @returns Reassembled script content as string
 */
export function reassembleScriptContent(chunks: any[]): string {
  try {
    console.log(`[ELEVENLABS] 📝 Reassembling script content from ${chunks.length} chunks`);

    if (chunks.length === 0) {
      throw new Error("No chunks provided for reassembly");
    }

    // Sort chunks by position or page_number
    let sortedChunks = [...chunks];
    if ("position" in chunks[0]) {
      sortedChunks.sort((a, b) => (a.position || 0) - (b.position || 0));
      console.log(`[ELEVENLABS] Sorted chunks by position field`);
    } else if ("metadata" in chunks[0] && "page_number" in chunks[0].metadata) {
      sortedChunks.sort((a, b) => (a.metadata.page_number || 0) - (b.metadata.page_number || 0));
      console.log(`[ELEVENLABS] Sorted chunks by metadata.page_number field`);
    } else {
      console.warn(`[ELEVENLABS] No sorting field found, using original order`);
    }

    // Determine content field name
    const contentField = "pageContent" in chunks[0] ? "pageContent" : "content";
    console.log(`[ELEVENLABS] Using content field: ${contentField}`);

    // Reassemble content
    const reassembledContent = sortedChunks
      .map((chunk) => chunk[contentField] || "")
      .join("\n");

    console.log(`[ELEVENLABS] ✅ Script reassembled: ${reassembledContent.length} characters`);
    return reassembledContent;

  } catch (error) {
    console.error(`[ELEVENLABS] Error reassembling script content:`, error);
    throw error;
  }
}

/**
 * Uploads reassembled script content to ElevenLabs knowledge base as text file
 * @param scriptContent - The reassembled script content
 * @param scriptName - Name of the script
 * @param apiKey - Optional API key
 * @returns Upload response with document and knowledge base IDs
 */
export async function uploadScriptContentToKnowledgeBase(
  scriptContent: string,
  scriptName: string,
  apiKey?: string
): Promise<CustomKnowledgeBaseUploadResponse> {
  try {
    console.log(`[ELEVENLABS] 📤 Uploading script content to knowledge base: ${scriptName}`);
    console.log(`[ELEVENLABS] Content length: ${scriptContent.length} characters`);

    const client = createElevenLabsClient(apiKey);

    // Create a text file from the script content
    const fileName = `${scriptName}.txt`;
    const fileBlob = new Blob([scriptContent], { type: 'text/plain' });
    const file = new File([fileBlob], fileName, { type: 'text/plain' });

    console.log(`[ELEVENLABS] Created text file: ${fileName} (${fileBlob.size} bytes)`);

    const response: any = await client.conversationalAi.addToKnowledgeBase({
      file,
      name: scriptName, // Use script name without extension for display
    });

    console.log("[ELEVENLABS] Raw SDK response from script upload:", response);

    let documentId: string;
    let knowledgeBaseId: string;

    if (response?.document_id && response?.knowledge_base_id) {
      documentId = response.document_id;
      knowledgeBaseId = response.knowledge_base_id;
    } else if (response?.id) {
      documentId = response.id;
      knowledgeBaseId = response.knowledge_base_id || "default";
    } else {
      console.error("[ELEVENLABS] Invalid response from script upload:", response);
      throw new Error("Invalid response from ElevenLabs API during script upload: Missing required IDs");
    }

    console.log(`[ELEVENLABS] ✅ Script uploaded successfully: ${scriptName} -> ${documentId}`);

    return {
      document_id: documentId,
      knowledge_base_id: knowledgeBaseId,
    };

  } catch (error) {
    console.error(`[ELEVENLABS] Error uploading script content to knowledge base:`, error);
    throw error;
  }
}

/**
 * Complete script synchronization workflow: Reassemble + Upload + Index + Associate
 * @param chunks - Firebase script chunks
 * @param scriptName - Name of the script
 * @param scriptNamespace - Firebase namespace/ID
 * @param agentId - User's agent ID
 * @param apiKey - Optional API key
 * @returns Synchronization result with document details
 */
export async function synchronizeScriptToAgent(
  chunks: any[],
  scriptName: string,
  scriptNamespace: string,
  agentId: string,
  apiKey?: string
): Promise<{
  success: boolean;
  documentId?: string;
  knowledgeBaseId?: string;
  ragIndexStatus?: string;
  error?: string;
}> {
  try {
    console.log(`[ELEVENLABS] 🔄 Starting script synchronization workflow:`, {
      scriptName,
      scriptNamespace,
      agentId,
      chunksCount: chunks.length
    });

    // Step 1: Reassemble script content
    console.log(`[ELEVENLABS] Step 1: Reassembling script content...`);
    const scriptContent = reassembleScriptContent(chunks);

    // Step 2: Upload to knowledge base
    console.log(`[ELEVENLABS] Step 2: Uploading to knowledge base...`);
    const uploadResponse = await uploadScriptContentToKnowledgeBase(scriptContent, scriptName, apiKey);

    // Step 3: Trigger RAG indexing
    console.log(`[ELEVENLABS] Step 3: Triggering RAG indexing...`);
    const ragIndexResult = await computeRagIndexViaSDK(uploadResponse.document_id, apiKey);

    // Step 4: Associate with agent
    console.log(`[ELEVENLABS] Step 4: Associating with agent...`);
    await updateAgentKnowledgeBase(
      agentId,
      uploadResponse.document_id,
      apiKey,
      DEFAULT_RAG_EMBEDDING_MODEL,
      DEFAULT_RAG_MAX_DOCUMENTS_LENGTH,
      scriptName
    );

    console.log(`[ELEVENLABS] ✅ Script synchronization completed successfully:`, {
      scriptName,
      documentId: uploadResponse.document_id,
      ragStatus: ragIndexResult.status
    });

    return {
      success: true,
      documentId: uploadResponse.document_id,
      knowledgeBaseId: uploadResponse.knowledge_base_id,
      ragIndexStatus: ragIndexResult.status
    };

  } catch (error) {
    console.error(`[ELEVENLABS] ❌ Script synchronization failed:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Test function for RAG indexing endpoints.
 * @param documentId - ID of an existing document to test RAG indexing with
 * @param apiKey - Optional API key
 * @param ragIndexingModel - Model for RAG indexing
 */
export async function testRagIndexingEndpoints(
  documentId: string,
  apiKey?: string,
  ragIndexingModel: string = DEFAULT_RAG_INDEXING_MODEL
): Promise<any> {
  try {
    console.log(`[ELEVENLABS] Testing RAG indexing endpoints for document ${documentId} using model ${ragIndexingModel}`);

    const docExists = await verifyDocumentExists(documentId, apiKey);
    if (!docExists) {
      console.warn(`[ELEVENLABS] Document ${documentId} does not exist or couldn't be fetched. Test may provide limited info.`);
    }

    console.log(`[ELEVENLABS] Testing with computeRagIndex (manual fetch)...`);
    const manualResult = await computeRagIndex(documentId, apiKey, ragIndexingModel);
    console.log(`[ELEVENLABS] Manual fetch RAG indexing test result:`, manualResult);

    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log(`[ELEVENLABS] Testing with computeRagIndexViaSDK...`);
    const sdkResult = await computeRagIndexViaSDK(documentId, apiKey, ragIndexingModel);
    console.log(`[ELEVENLABS] SDK RAG indexing test result:`, sdkResult);

    return {
      documentId,
      documentVerified: docExists,
      manualFetchResult: manualResult,
      sdkResult: sdkResult,
    };

  } catch (error) {
    console.error(`[ELEVENLABS] Error testing RAG indexing endpoints for document ${documentId}:`, error);
    return {
      documentId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    };
  }
}

/**
 * Debug function to test agent configuration and flag placement
 * This can be called from the browser console for testing
 * @param agentId - ID of the agent to test
 * @param apiKey - Optional API key
 */
export async function debugAgentConfiguration(agentId: string, apiKey?: string): Promise<void> {
  try {
    console.log(`[DEBUG_AGENT_CONFIG] 🔍 Starting debug for agent: ${agentId}`);

    const config = await getAgentConfiguration(agentId, apiKey);

    console.log(`[DEBUG_AGENT_CONFIG] 📋 Full agent configuration:`, JSON.stringify(config, null, 2));

    console.log(`[DEBUG_AGENT_CONFIG] 🔍 Client events configuration check:`, {
      'client_events': config.conversation_config?.conversation?.client_events || [],
      'has_agent_response': (config.conversation_config?.conversation?.client_events || []).includes('agent_response'),
      'has_user_transcript': (config.conversation_config?.conversation?.client_events || []).includes('user_transcript'),
      'conversation_exists': !!config.conversation_config?.conversation,
      'conversation_config_exists': !!config.conversation_config
    });

    console.log(`[DEBUG_AGENT_CONFIG] 📊 Configuration structure:`, {
      'has_conversation_config': !!config.conversation_config,
      'conversation_config_keys': config.conversation_config ? Object.keys(config.conversation_config) : 'N/A',
      'has_platform_settings': !!config.platform_settings,
      'platform_settings_keys': config.platform_settings ? Object.keys(config.platform_settings) : 'N/A',
      'root_keys': Object.keys(config)
    });

  } catch (error) {
    console.error(`[DEBUG_AGENT_CONFIG] ❌ Error debugging agent configuration:`, error);
  }
}

// Make debug function available globally for testing
if (typeof window !== 'undefined') {
  (window as any).debugAgentConfiguration = debugAgentConfiguration;
  console.log('[DEBUG_AGENT_CONFIG] 🌐 Debug function available globally as window.debugAgentConfiguration(agentId)');
}