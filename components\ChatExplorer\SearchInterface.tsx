'use client'

import React, { useState, useEffect, ChangeEvent, KeyboardEvent } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { SearchResult } from './ChatVectorTypes'
import SearchHeader from './SearchHeader'
import ResultsGrid from './ResultsGrid'
import {BookOpenIcon} from '@heroicons/react/24/solid';
import { 
  collection, 
  query as firestoreQuery,
  where,
  orderBy, 
  doc, 
  getDoc,
  getDocs,
  addDoc, 
  onSnapshot,
  Timestamp,
  Unsubscribe,
  setDoc,
  serverTimestamp
} from 'firebase/firestore'
import { db } from 'components/firebase'
import { format, subDays, isToday, isYesterday, isWithinInterval } from 'date-fns'
import { ExternalLink, Grid, List, ChevronDown, ChevronRight, FilesIcon, FolderIcon, FoldersIcon, MessageSquare } from 'lucide-react'

type ViewMode = 'cards' | 'table'

interface ChatHistoryEntry {
  id: string
  createdAt: number
  category: string
  fileName: string
  chatPreview: string
  namespace: string
  chatId: string
  fileDocumentId: string
}

interface ChatData {
  createdAt: Timestamp
  fileDocumentId?: string
}

interface FileData {
  category?: string
  name?: string
  namespace?: string
}

interface MessageData {
  role: string
  text: string
}

interface GroupedChatHistory {
  [key: string]: ChatHistoryEntry[]
}

export default function SearchInterface() {
  const router = useRouter()
  const [query, setQuery] = useState<string>("")
  const [results, setResults] = useState<SearchResult[]>([])
  const [chatHistory, setChatHistory] = useState<ChatHistoryEntry[]>([])
  const [groupedChatHistory, setGroupedChatHistory] = useState<GroupedChatHistory>({})
  const [isSearching, setIsSearching] = useState<boolean>(false)
  const [isLoadingHistory, setIsLoadingHistory] = useState<boolean>(false)
  const [viewMode, setViewMode] = useState<ViewMode>('table')
  const [expandedGroups, setExpandedGroups] = useState<{ [key: string]: boolean }>({})
  const { data: session } = useSession()

  useEffect(() => {
    const userEmail = session?.user?.email
    if (!userEmail) {
      return
    }

    setIsLoadingHistory(true)
    const messageUnsubscribes: Unsubscribe[] = []
    
    try {
      const q = firestoreQuery(
        collection(db, 'users', userEmail, 'chats'),
        orderBy('createdAt', 'desc')
      )

      const unsubscribe = onSnapshot(q, async (querySnapshot) => {
        const newChatHistory: ChatHistoryEntry[] = []
        
        messageUnsubscribes.forEach(unsub => unsub())
        messageUnsubscribes.length = 0

        for (const chatDoc of querySnapshot.docs) {
          const chatData = chatDoc.data() as ChatData
          
          const messagesQuery = firestoreQuery(
            collection(db, 'users', userEmail, 'chats', chatDoc.id, 'messages')
          )

          const messagesUnsubscribe = onSnapshot(messagesQuery, async (messagesSnapshot) => {
            const aiMessage = messagesSnapshot.docs.find(doc => 
              (doc.data() as MessageData).role === 'ai'
            )
            
            let fileData: FileData = {}
            if (chatData.fileDocumentId) {
              const fileDocRef = doc(db, 'users', userEmail, 'files', chatData.fileDocumentId)
              try {
                const fileDocSnap = await getDoc(fileDocRef)
                if (fileDocSnap.exists()) {
                  fileData = fileDocSnap.data() as FileData
                }
              } catch (error) {
                console.error("Error fetching file data:", error)
              }
            }

            const chatEntry: ChatHistoryEntry = {
              id: chatDoc.id,
              createdAt: chatData.createdAt?.toMillis() || Date.now(),
              category: fileData?.category || 'Unknown',
              fileName: fileData?.name || 'Untitled',
              chatPreview: (aiMessage?.data() as MessageData)?.text || 'No preview available',
              namespace: fileData?.namespace || fileData?.category || 'General',
              chatId: chatDoc.id,
              fileDocumentId: chatData.fileDocumentId || ''
            }

            setChatHistory(prev => {
              const filtered = prev.filter(entry => entry.id !== chatEntry.id)
              return [...filtered, chatEntry].sort((a, b) => b.createdAt - a.createdAt)
            })
          })

          messageUnsubscribes.push(messagesUnsubscribe)
        }

        setIsLoadingHistory(false)
      })

      return () => {
        unsubscribe()
        messageUnsubscribes.forEach(unsub => unsub())
      }
    } catch (error) {
      console.error("Failed to fetch chat history:", error)
      setIsLoadingHistory(false)
    }
  }, [session?.user?.email])

  useEffect(() => {
    const grouped = chatHistory.reduce((acc: GroupedChatHistory, chat) => {
      const date = new Date(chat.createdAt)
      let group = ''

      if (isToday(date)) {
        group = 'Today'
      } else if (isYesterday(date)) {
        group = 'Yesterday'
      } else if (isWithinInterval(date, { start: subDays(new Date(), 7), end: subDays(new Date(), 2) })) {
        group = 'Last 7 days'
      } else if (isWithinInterval(date, { start: subDays(new Date(), 30), end: subDays(new Date(), 8) })) {
        group = 'Previous 30 days'
      } else {
        group = 'Over a month'
      }

      if (!acc[group]) {
        acc[group] = []
      }
      acc[group].push(chat)
      return acc
    }, {})

    const orderedGroups = ['Today', 'Yesterday', 'Last 7 days', 'Previous 30 days', 'Over a month']
    const orderedGroupedHistory = orderedGroups.reduce((acc, key) => {
      if (grouped[key]) {
        acc[key] = grouped[key]
      }
      return acc
    }, {} as GroupedChatHistory)

    setGroupedChatHistory(orderedGroupedHistory)
    setExpandedGroups(orderedGroups.reduce((acc, key) => ({ ...acc, [key]: key === 'Today' }), {}))
  }, [chatHistory])

  const handleQueryChange = (e: ChangeEvent<HTMLInputElement>): void => {
    setQuery(e.target.value)
  }

  const handleKeyPress = (e: KeyboardEvent<Element>): void => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  const logSearch = async (userEmail: string, searchQuery: string, resultCount: number) => {
    try {
      const userDocRef = doc(db, 'users', userEmail)
      const searchStatsRef = doc(userDocRef, 'searchStats', 'history')

      await setDoc(searchStatsRef, {
        recentSearches: [{
          query: searchQuery,
          timestamp: new Date(),
          resultCount: resultCount
        }]
      }, { merge: true })

    } catch (error) {
      console.error('Error logging search:', error)
    }
  }

  const handleSearch = async (): Promise<void> => {
    if (!query.trim()) return

    setIsSearching(true)
    setResults([])
    setViewMode('cards')

    try {
      const response = await fetch("/api/searchChat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          query,
          topK: 12,
        }),
      })
      const data = await response.json()
      setResults(data.results)
      
      if (session?.user?.email) {
        await logSearch(session.user.email, query, data.results.length)
      }
    } catch (error) {
      console.error("Search failed:", error)
    } finally {
      setIsSearching(false)
    }
  }

  const toggleGroup = (group: string) => {
    setExpandedGroups(prev => ({ ...prev, [group]: !prev[group] }))
  }

  const createNewChat = async (fileName: string, category: string) => {
    if (!session?.user?.email) {
      console.error('User email is missing.')
      return
    }

    try {
      const filesQuery = firestoreQuery(
        collection(db, 'users', session.user.email, 'files'),
        where('name', '==', fileName)
      )

      const filesSnapshot = await getDocs(filesQuery)
      //console.log(`Found ${filesSnapshot.size} files with name ${fileName}`)

      if (filesSnapshot.empty) {
        console.error('No files found with name:', fileName)
        return
      }

      const fileDocumentId = filesSnapshot.docs[0].id

      const chatDocRef = await addDoc(
        collection(db, 'users', session.user.email, 'chats'),
        {
          userId: session.user.email,
          createdAt: serverTimestamp(),
          fileDocumentId: fileDocumentId,
          firstMessage: 'Begin messaging...',
          category: category
        }
      )

      router.push(`/chat/${chatDocRef.id}`)

    } catch (err) {
      console.error('Error creating new chat:', err)
    }
  }

  const renderTableView = () => (
    <div className="overflow-x-auto rounded-lg">
      <table className="w-full text-sm text-left text-gray-300">
        <thead className="text-[10px] uppercase bg-gray-900 text-gray-400">
          <tr>
            <th scope="col" className="w-[140px] pt-2">
              <div className="px-3 py-2 border border-gray-500 shadow-md rounded-2xl inline-flex items-center m-1">
                Date
              </div>
            </th>
            <th scope="col" className="w-[160px] pt-2">
              <div className="px-3 py-2 border border-gray-500 shadow-md rounded-2xl inline-flex items-center m-1">
              <FoldersIcon className="h-5 w-5 mr-2" />
                Folders
              </div>
            </th>
            <th scope="col" className="w-[300px] pt-2">
              <div className="px-3 py-2 border border-gray-500 shadow-md rounded-2xl inline-flex items-center m-1">
              <FilesIcon className="h-5 w-5 mr-2" />
                FILES
              </div>
            </th>
            <th scope="col" className="min-w-[300px] max-w-[400px] pt-2">
              <div className="px-3 py-2 border border-gray-500 shadow-md rounded-2xl inline-flex items-center m-1">
              <MessageSquare className="h-5 w-5 mr-2" />
                Chat Preview
              </div>
            </th>
            <th scope="col" className="w-[120px] pt-2">
              <div className="px-3 py-2 border border-gray-500 shadow-md rounded-2xl inline-flex items-center ml-8">
                Actions
              </div>
            </th>
          </tr>
        </thead>
        <tbody>
          {isLoadingHistory ? (
            <>
              {[...Array(5)].map((_, index) => (
                <tr key={index} className="border-b border-gray-700 animate-pulse">
                  <td className="px-4 py-3">
                    <div className="h-4 bg-gray-600 rounded w-24"></div>
                  </td>
                  <td className="px-4 py-3">
                    <div className="h-4 bg-gray-600 rounded w-20"></div>
                  </td>
                  <td className="px-4 py-3">
                    <div className="h-4 bg-gray-600 rounded w-40"></div>
                  </td>
                  <td className="px-4 py-3">
                    <div className="h-4 bg-gray-600 rounded w-60"></div>
                  </td>
                  <td className="px-4 py-3">
                    <div className="h-4 bg-gray-600 rounded w-16"></div>
                  </td>
                </tr>
              ))}
            </>
          ) : Object.keys(groupedChatHistory).length === 0 ? (
            <tr>
              <td colSpan={6} className="px-6 py-8 text-center text-gray-500">
                No chat history found
              </td>
            </tr>
          ) : (
            Object.entries(groupedChatHistory).map(([group, entries]) => (
              <React.Fragment key={group}>
                <tr className="bg-gray-900">
                  <td colSpan={6} className="px-6 py-4">
                    <button
                      className="flex items-center gap-2 font-semibold text-amber-500 hover:text-white -ml-4 -mb-2 -mt-2"
                      onClick={() => toggleGroup(group)}
                    >
                      {expandedGroups[group] ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                      {group}
                    </button>
                  </td>
                </tr>
                {expandedGroups[group] && entries.map((entry) => (
                  <tr key={entry.id} className="border-b border-gray-700 hover:bg-ike-message-bg text-sm">
                    <td className="px-4 py-1 whitespace-nowrap">
                      {format(entry.createdAt, 'd MMM, yyyy')}
                    </td>
                    <td className="px-4 py-1">
                      <span className="px-1.5 py-0.5 rounded-full text-[12px] bg-ike-purple">
                        {entry.category  === 'Unknown' ? 'General' : entry.category}
                      </span>
                    </td>
                    <td className="px-4 py-1">
                      <div className="max-w-[200px] truncate" title={entry.fileName}>
                        {entry.fileName}
                      </div>
                    </td>
                    <td className="px-4 py-1">
                      <div 
                        className="max-w-md cursor-pointer hover:text-blue-400 truncate"
                        onClick={() => router.push(`/chat/${entry.chatId}`)}
                        title={entry.chatPreview}
                      >
                        {entry.chatPreview}
                      </div>
                    </td>
                    <td className="px-4 py-1 text-right">
                      <div className="flex items-center justify-end gap-1">
                        <button
                          onClick={() => router.push(`/chat/${entry.chatId}`)}
                          className="p-0.5 hover:bg-gray-600 rounded-md transition-colors"
                          title="Open chat"
                        >
                          <ExternalLink className="h-3 w-3" />
                          <span className="sr-only">Open chat</span>
                        </button>
                        <button
                          onClick={() => createNewChat(entry.fileName, entry.category)}
                          className="px-2 py-0.5 text-[10px] bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
                        >
                          New Chat
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </React.Fragment>
            ))
          )}
        </tbody>
      </table>
    </div>
  )

  return (
    <div className="min-h-screen bg-black bg-opacity-65 text-white p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        <SearchHeader
          query={query}
          isSearching={isSearching}
          onQueryChange={handleQueryChange}
          onKeyPress={handleKeyPress}
          onSearch={handleSearch}
        />
        
        <div className="flex items-center gap-2 mb-4">
          <button
            onClick={() => setViewMode('cards')}
            className={`flex items-center gap-2 px-3 py-2 rounded-2xl text-sm font-medium transition-colors ${
              viewMode === 'cards'
                ? 'bg-blue-600 text-white'
                : ' text-gray-300 hover:bg-gray-300 hover:text-ike-message-bg border border-ike-message-bg'
            }`}
          >
            <Grid className="h-4 w-4" />
            Cards View
          </button>
          <button
            onClick={() => setViewMode('table')}
            className={`flex items-center gap-2 px-3 py-2 rounded-2xl text-sm font-medium transition-colors ${
              viewMode === 'table'
                ? 'bg-blue-600 text-white'
                : ' text-gray-300 hover:bg-gray-300 hover:text-ike-message-bg border border-ike-message-bg'
            }`}
          >
            <List className="h-4 w-4" />
            Table View
          </button>
      
          <button
            onClick={() => router.push('/fileManager')}
         
            className="flex items-center gap-2 px-3 py-2 rounded-2xl text-sm font-medium 
             text-gray-300 hover:bg-gray-300 hover:text-ike-message-bg border
              border-ike-message-bg transition-colors"
          >
            <FoldersIcon className="h-4 w-4" />
            Folders 
           
          </button>          
        </div>

        {viewMode === 'cards' ? (
          <ResultsGrid results={results} isLoading={isSearching} />
        ) : (
          renderTableView()
        )}
      </div>
    </div>
  )
}