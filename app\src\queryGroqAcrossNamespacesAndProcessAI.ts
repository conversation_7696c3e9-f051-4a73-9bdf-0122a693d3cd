import { AnalyticsMiddleware } from "lib/analytics/AnalyticsMiddleware";
import { createPrompt, getSystemPrompt } from './prompting/aiprompting';
import { TokenManagement } from './tokenTracker/tokenManagement';
import { StreamOptions } from "@/src/types/shared";
import { getServerSession } from "next-auth";
import { authOptions } from "../api/auth/[...nextauth]/authOptions";
import { ProcessChatHistoryTool } from "components/tools/ProcessChatHistoryTool";
import { ProcessContentTool } from "components/tools/ProcessContentTool";
import { UpdateContentProcessingTool } from "components/tools/UpdateContentProcessingTool";
import { MetadataPayloadTool } from "components/tools/MetadataPayloadTool";
import { StreamToClientTool } from "components/tools/StreamToClientTool";
import { SupervisorAgent } from "components/Agents/SupervisorAgent";
import { VisualizationAgent } from "components/Agents/VisualizationAgent";
import { ProcessGroqMessagesAgent } from "components/Agents/ProcessGroqMessagesAgent";
import { GenerateImageTool } from "components/tools/generateImageTool";

const MAX_CONTENT_PREVIEW_LENGTH = 800;
const MIN_RELEVANCE_THRESHOLD = 0.7;

interface Source {
  title: string;
  page: number;
  doc_id: string;
  relevance: number;
}

interface BaseMessage {
  role: "user" | "ai";
  content: string;
  text: string;
}

interface ProcessContentOutput {
  success: boolean;
  content: string;
  metadata: {
    sources: Source[];
    totalTokens: number;
    chunkCount: number;
    averageRelevance: number;
    namespaceDistribution: Record<string, number>;
  };
}

interface CombinedMetadata {
  sources: Source[];
  totalTokens?: number;
  chunkCount?: number;
  averageRelevance?: number;
  namespaceDistribution?: Record<string, number>;
  internetSearch?: {
    performed: boolean;
    sources: string[];
    relevance: number;
  };
  visualizationData?: Record<string, unknown>[];
  visualization?: {
    type: 'chart' | 'table';
    config: Record<string, unknown>;
    data: Record<string, unknown>[];
  };
}

interface ChatHistoryResult {
  success: boolean;
  chatHistoryArray: Array<{ role: string; content: string }>;
}

interface SupervisorResult {
  success: boolean;
  error?: string;
  data?: {
    chatHistory: ChatHistoryResult;
    internetSearchResults?: {
      content: string;
      sources: string[];
      relevance: number;
    };
    visualization?: {
      type: 'chart' | 'table';
      config: Record<string, unknown>;
      data: Record<string, unknown>[];
    };
  };
}

interface InternetSearchResult {
  success: boolean;
  error?: string;
  content?: string;
  sources?: string[];
  relevance?: number;
}

export async function queryGroqAcrossNamespacesAndProcessAI(
  controller: ReadableStreamDefaultController<any>,
  queryVector: number[],
  namespaces: string[] | null,
  userQuery: string,
  rawChatHistory: string,
  category: string | null,
  userId: string
): Promise<void> {
  const streamOptions: StreamOptions = {
    controller,
    streamId: ""
  };
  const analytics = new AnalyticsMiddleware();
  const tokenManager = new TokenManagement(analytics);

  // Initialize tools
  const streamTool = new StreamToClientTool();
  const chatHistoryTool = new ProcessChatHistoryTool(tokenManager, {
    maxHistoryMessages: 10,
    maxTokensPerMessage: 500
  });
  const contentTool = new ProcessContentTool(userId, tokenManager);
  const updateContentTool = new UpdateContentProcessingTool(tokenManager);
  const metadataPayloadTool = new MetadataPayloadTool();

  // Initialize agents with proper configurations
  const visualizationAgent = new VisualizationAgent();

  const groqMessagesAgent = new ProcessGroqMessagesAgent(streamTool, visualizationAgent);
  const generateImageTool = new GenerateImageTool(process.env.OPENAI_API_KEY || '');

  const supervisor = new SupervisorAgent(
    {
      tokenManagement: tokenManager,
      streamOptions
    },
    {
      chatHistoryTool,
      streamTool,
      groqMessagesTool: groqMessagesAgent,
      visualizationAgent,
      generateImageTool
    }
  );

  try {
    // Start analytics tracking
    await analytics.interceptQueryStart(
      userId,
      namespaces,
      userQuery,
      rawChatHistory,
      category
    );

    // Validate session
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      throw new Error("Unauthorized - No valid session");
    }

    // Process with supervisor
    const supervisorResult = await supervisor.process({
      queryVector,
      namespaces,
      userQuery,
      rawChatHistory: typeof rawChatHistory === 'string' ? rawChatHistory : JSON.stringify(rawChatHistory),
      category,
      userId,
      streamOptions
    });

    const transformedResult: SupervisorResult = {
      success: supervisorResult.success,
      error: supervisorResult.error,
      data: supervisorResult.data ? {
        chatHistory: supervisorResult.data.chatHistory as ChatHistoryResult,
        internetSearchResults: supervisorResult.data.internetSearchResults,
        visualization: supervisorResult.data.visualization ? {
          type: supervisorResult.data.visualization.content.type,
          config: supervisorResult.data.visualization.content.config,
          data: supervisorResult.data.visualization.content.data || []
        } : undefined
      } : undefined
    };

    if (!transformedResult.success) {
      throw new Error(`SupervisorAgent processing failed: ${transformedResult.error}`);
    }

    const chatHistory = transformedResult.data?.chatHistory;
    if (!chatHistory?.success) {
      throw new Error("Chat history processing failed");
    }

    // Process internal documents
    //console.log("Searching internal documents...");
    const contentResult = await contentTool._call({
      queryVector,
      namespaces: namespaces || [],
      userId
    }) as ProcessContentOutput;

    if (contentResult.success) {
      await updateContentTool._call({
        totalTokens: contentResult.metadata.totalTokens,
        chunkCount: contentResult.metadata.chunkCount,
        averageRelevance: contentResult.metadata.averageRelevance,
        namespaceDistribution: contentResult.metadata.namespaceDistribution
      });
    }

    // Initialize metadata and content
    let combinedContent = '';
    let combinedMetadata: CombinedMetadata = {
      sources: [],
      totalTokens: 0,
      chunkCount: 0,
      averageRelevance: 0,
      namespaceDistribution: {},
      internetSearch: {
        performed: false,
        sources: [],
        relevance: 0
      }
    };

    // Process content results
    if (contentResult.success) {
      combinedContent = contentResult.content;
      combinedMetadata = {
        ...contentResult.metadata,
        sources: contentResult.metadata.sources.map(source => ({
          title: source.title,
          page: source.page,
          doc_id: source.doc_id,
          relevance: source.relevance
        })),
        internetSearch: {
          performed: false,
          sources: [],
          relevance: 0
        }
      };

      const needsInternetSearch =
        contentResult.metadata.averageRelevance < MIN_RELEVANCE_THRESHOLD ||
        !contentResult.content.trim();

      if (needsInternetSearch) {
        //console.log(`Internal content insufficient (Relevance: ${contentResult.metadata.averageRelevance}), attempting internet search...`);
        const internetResults: InternetSearchResult = await supervisor.performInternetSearch(userQuery);

        if (internetResults?.success && internetResults.content) {
          combinedContent += `\n\nAdditional Current Information:\n${internetResults.content}`;
          combinedMetadata = {
            ...combinedMetadata,
            sources: [
              ...combinedMetadata.sources,
              ...(internetResults.sources || []).map(source => ({
                title: source,
                page: 1,
                doc_id: source,
                relevance: internetResults.relevance || 0
              }))
            ],
            internetSearch: {
              performed: true,
              sources: internetResults.sources || [],
              relevance: internetResults.relevance || 0
            }
          };
        }
      }
    } else {
      // Fallback to internet search if internal search fails
      //console.log("Internal search failed, attempting internet search...");
      const internetResults: InternetSearchResult = await supervisor.performInternetSearch(userQuery);

      if (internetResults?.success && internetResults.content) {
        combinedContent = internetResults.content;
        combinedMetadata = {
          sources: (internetResults.sources || []).map(source => ({
            title: source,
            page: 1,
            doc_id: source,
            relevance: internetResults.relevance || 0
          })),
          internetSearch: {
            performed: true,
            sources: internetResults.sources || [],
            relevance: internetResults.relevance || 0
          }
        };
      } else {
        await streamTool.streamFallback(streamOptions);
        return;
      }
    }

    // Process metadata
    const metadataResult = await metadataPayloadTool._call({
      content: combinedContent,
      metadata: {
        sources: combinedMetadata.sources,
        totalTokens: combinedMetadata.totalTokens,
        chunkCount: combinedMetadata.chunkCount,
        averageRelevance: combinedMetadata.averageRelevance,
        namespaceDistribution: combinedMetadata.namespaceDistribution
      },
      tokenManager,
      maxPreviewLength: MAX_CONTENT_PREVIEW_LENGTH
    });

    // Stream metadata to client
    await streamTool.streamMetadata(
      streamOptions,
      metadataResult.success
        ? metadataResult.payload
        : JSON.stringify({
            pageContent: combinedContent.substring(0, MAX_CONTENT_PREVIEW_LENGTH) + "...",
            error: "Failed to generate full metadata"
          })
    );

    // Create prompt for Groq
    const prompt = await createPrompt({
      systemPrompt: getSystemPrompt(),
      context: combinedContent,
      metadata: {
        sources: combinedMetadata.sources,
        tokenUsage: tokenManager.getCurrentUsage(),
        relevance: combinedMetadata.averageRelevance ||
                  (combinedMetadata.internetSearch?.relevance || 0),
        internetSearch: combinedMetadata.internetSearch?.performed || false
      },
      chatHistory: chatHistory.chatHistoryArray.map(message => ({
        role: message.role as "user" | "ai",
        content: message.content,
        text: message.content
      })),
      userQuery,
    });

    // Process with Groq through supervisor
    const groqResult = await supervisor.processGroqMessages({
      userEmail: session.user.email,
      streamOptions,
      prompt,
      config: {
        temperature: 0.3,
        maxTokens: 5000
      },
      userId
    });

    if (!groqResult.success) {
      throw new Error(groqResult.error || "Failed to process Groq messages");
    }

    if (groqResult.visualization) {
      combinedMetadata.visualization = groqResult.visualization;
    }

  } catch (error) {
    console.error("Error in queryGroqAcrossNamespacesAndProcessAI:", error);
    if (error instanceof Error) {
      console.error({
        errorName: error.name,
        errorMessage: error.message,
        errorStack: error.stack
      });
      await streamTool.streamError(streamOptions, error);
    }
  }
}