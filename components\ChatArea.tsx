"use client";

import React, { useEffect, useRef, useState } from "react";
import { collection, addDoc, serverTimestamp, doc, getDoc, updateDoc } from "firebase/firestore";
import { db } from "components/firebase";
import { Session } from "next-auth";
import Message from "./Message";
import MessageInput from "./MessageInput";
import InitialResponses from "./initialResponses";
import initialResponses from "./initialResponsesData";
import { getChatHistory } from "./firebaseClient";
import PdfDocumentArea from "./PdfDocumentArea";
import { AlertCircle } from 'lucide-react';
import { DEFAULT_CATEGORY } from "components/ChatExplorer/ChatVectorTypes";
import VoiceChat from "./VoiceAssistants/VoiceComponent";

interface ChatAreaProps {
  initialMessages: ChatData[];
  session: Session | null;
  loadingMessages: boolean;
  errorMessages: string | null;
  isPending: boolean;
  selectedDocId: string | null;
  category: string;
}

export interface ChatData {
  category: string;
  fileName: string;
  id: string;
  text: string;
  userId: string;
  role: "user" | "ai";
  createdAt: {
    seconds: number;
    nanoseconds: number;
  };
  fileDocumentId?: string | null;
  stopped?: boolean;
  visualizationUrl?: string;
}

interface ChatMessage {
  id: string;
  text: string;
  role: "user" | "ai";
  createdAt: {
    seconds: number;
    nanoseconds: number;
  };
  fileDocumentId: string;
  fileName: string;
  category: string;
  userId: string;
}

interface ProcessingProgress {
  processedChats: number;
  totalChats: number;
}


const SUPPORTED_IMAGE_TYPES = ["image/jpeg", "image/png", "image/gif", "image/webp"];
const isImageFile = (fileType: string): boolean => {
  return SUPPORTED_IMAGE_TYPES.includes(fileType);
};

const ImageDisplay = ({
  imageUrl,
  docTitle,
  fileType,
}: {
  imageUrl: string;
  docTitle: string;
  fileType: string;
}) => (
  <div className="flex flex-col items-center justify-center bg-ike-message-bg p-3 pb-2 rounded-lg mb-2 mx-4">
    <div className="flex items-center justify-between w-auto mb-2">
      <h3 className="text-amber-400 text-sm">{docTitle}</h3>
    </div>
    <div className="relative w-max mx-auto bg-opacity-20 rounded-lg p-2">
      <img
        src={imageUrl}
        alt={docTitle}
        className="rounded-lg shadow-lg w-full h-auto object-contain max-h-[35vh]"
        onError={(e) => {
          e.currentTarget.src = "/placeholder-image.png";
          e.currentTarget.alt = "Failed to load image";
        }}
      />
    </div>
  </div>
);




const fetchCurrentDocumentId = async (
  session: Session | null,
  chatId: string | null
): Promise<string | null> => {
  if (!session || !chatId) return null;
  const chatDocRef = doc(db, "users", session.user?.email!, "chats", chatId);
  const chatDocSnap = await getDoc(chatDocRef);

  if (chatDocSnap.exists()) {
    return chatDocSnap.data()?.fileDocumentId || null;
  }
  return null;
};

export default function ChatArea({
  initialMessages,
  session,
  loadingMessages,
  errorMessages,
  isPending: propIsPending,
  selectedDocId: propSelectedDocId,
  category,
}: ChatAreaProps) {
  const [isPending, setIsPending] = useState<boolean>(propIsPending);
  const [selectedDocId, setSelectedDocId] = useState<string | null>(propSelectedDocId);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [messages, setMessages] = useState<ChatData[]>(initialMessages || []);
  const [message, setMessage] = useState<string>("");
  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(null);
  const [streamingText, setStreamingText] = useState<string>("");
  const [fileDocumentId, setFileDocumentId] = useState<string | null>(null);
  const [pageContent, setPageContent] = useState<string>("");
  const [pageNumber, setPageNumber] = useState<string | null>(null);
  const [docTitle, setDocTitle] = useState<string>("Unknown Document");
  const [firstValidUserMessage, setFirstValidUserMessage] = useState<string | null>(null);
  const [firstValidAIResponse, setFirstValidAIResponse] = useState<string | null>(null);
  const [isFirstMessage, setIsFirstMessage] = useState<boolean>(true);
  const [showPdfArea, setShowPdfArea] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [abortController, setAbortController] = useState<AbortController | null>(null);
  const [isStreaming, setIsStreaming] = useState<boolean>(false);
  const [currentCategory, setCurrentCategory] = useState<string>(category || DEFAULT_CATEGORY);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [fileType, setFileType] = useState<string>("");
  const [isImageDoc, setIsImageDoc] = useState(false);
  const [processingProgress, setProcessingProgress] = useState<ProcessingProgress | null>(null);
  const [fileName, setFileName] = useState<string>("");
  const [resetWelcome, setResetWelcome] = useState(false); // Added state for welcome reset
  const [isVoiceActive, setIsVoiceActive] = useState(false);


  useEffect(() => {
    setCurrentCategory(category || DEFAULT_CATEGORY);
  }, [category]);

  useEffect(() => {
    const fetchDocumentDetails = async () => {
      if (!session?.user?.email || !selectedDocId) return;
      try {
        const chatDocRef = doc(db, "users", session.user.email, "chats", selectedDocId);
        const chatDoc = await getDoc(chatDocRef);
        if (!chatDoc.exists()) {
          //console.log("Chat document not found");
          return;
        }
        const fileDocumentId = chatDoc.data().fileDocumentId;

        const fileDocRef = doc(db, "users", session.user.email, "files", fileDocumentId);
        const fileDoc = await getDoc(fileDocRef);
        if (!fileDoc.exists()) {
          //console.log("File document not found");
          return;
        }
        const fileData = fileDoc.data();
        const documentName = fileData.name || "Untitled Document";
        setDocTitle(documentName);
        setFileType(fileData.type || "Unknown type");

        const isImage = fileData.isImage || isImageFile(fileData.type);
        setIsImageDoc(isImage);

        if (isImage && fileData.downloadUrl) {
          setImageUrl(fileData.downloadUrl);
        } else {
          setImageUrl(null);
        }

        setFileName(documentName);
      } catch (error) {
        console.error("Error fetching document details:", error);
        setError("Failed to load document details");
        setImageUrl(null);
      }
    };

    fetchDocumentDetails();
  }, [session?.user?.email, selectedDocId]);

  useEffect(() => {
    const fetchDocumentId = async () => {
      try {
        const documentId = await fetchCurrentDocumentId(session, selectedDocId);
        setFileDocumentId(documentId);
      } catch (error) {
        console.error("Error fetching document ID:", error);
        setError("Failed to fetch document ID. Please try again.");
      }
    };
    fetchDocumentId();
  }, [session, selectedDocId]);

  useEffect(() => {
    const fetchChatHistoryData = async () => {
      if (!session || !selectedDocId) return;
      try {
        const loadedMessages = await getChatHistory(session?.user?.email!, selectedDocId);
        setMessages(loadedMessages as ChatData[]);
        setIsFirstMessage(loadedMessages.length === 0);
      } catch (error) {
        console.error("Error fetching chat history:", error);
        setError("Failed to load chat history. Please refresh the page.");
      }
    };

    fetchChatHistoryData();
  }, [session, selectedDocId]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, streamingText]);

  const saveUserMessage = async (userMessage: ChatData) => {
    //console.log("DEBUG: Saving user message:", userMessage);
    const userEmail = session?.user?.email!;
    await addDoc(collection(db, "users", userEmail, "chats", selectedDocId!, "messages"), {
      ...userMessage,
      createdAt: serverTimestamp(),
      fileDocumentId,
    });
  };

  const saveAIMessage = async (aiMessage: ChatData) => {
    //console.log("DEBUG: Saving AI message:", aiMessage);
    const userEmail = session?.user?.email!;
    await addDoc(collection(db, "users", userEmail, "chats", selectedDocId!, "messages"), {
      ...aiMessage,
      createdAt: serverTimestamp(),
      fileDocumentId,
    });
  };

  const updateFirstMessage = async (messageText: string) => {
    if (!session || !selectedDocId) return;
    const chatDocRef = doc(db, "users", session.user?.email!, "chats", selectedDocId);
    await updateDoc(chatDocRef, { firstMessage: messageText });
  };

  const handleListItemClick = async (text: string) => {
    setIsPending(true)
    try {
      const optimizedText = await optimizeQuestion(text)
      handleSendMessage(optimizedText, '')
    } catch (error) {
      console.error('Error in handleListItemClick:', error)
      handleSendMessage(text, '')
    } finally {
      setIsPending(false)
    }
  }

  const optimizeQuestion = async (text: string): Promise<string> => {
    try {
      const response = await fetch('/api/optimizeQuestion', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text }),
      })

      if (!response.ok) {
        throw new Error('Failed to optimize question')
      }

      const data = await response.json()
      return data.optimizedText
    } catch (error) {
      console.error('Error optimizing question:', error)
      return text
    }
  }

  const handleSendMessage = async (messageText: string, menuSelected: string) => {
    if (messageText.trim()) {
      setIsPending(true);
      setError(null);
      const userEmail = session?.user?.email!;

      try {
        const chatHistory = await getChatHistory(userEmail, selectedDocId!);
        const combinedHistory = chatHistory
          .map((msg) => `${msg.role === "user" ? "User: " : "AI: "}${msg.text}`)
          .join("\n");

        const userMessage: ChatData = {
          id: `${Date.now()}`,
          text: messageText,
          userId: session?.user?.email || "user",
          role: "user",
          createdAt: {
            seconds: Math.floor(Date.now() / 1000),
            nanoseconds: 0,
          },
          fileDocumentId,
          category: currentCategory,
          fileName,
        };

        await saveUserMessage(userMessage);
        setMessages((prev) => [...prev, userMessage]);
        setMessage("");
        setStreamingMessageId(null);
        setStreamingText("");

        if (isFirstMessage) {
          await updateFirstMessage(messageText);
          setIsFirstMessage(false);
        }
        if (!firstValidUserMessage) {
          setFirstValidUserMessage(messageText);
        }

        const newAbortController = new AbortController();
        setAbortController(newAbortController);

        await fetchAIResponse(combinedHistory, messageText, menuSelected, newAbortController.signal);
      } catch (error) {
        console.error("Error sending message:", error);
        setError("Failed to send message. Please try again.");
      } finally {
        setIsPending(false);
        setAbortController(null);
      }
    }
  };

  const handleToggleVoice = (active: boolean) => {
    setIsVoiceActive(active);
  };

  const handleVoiceInput = async (transcribedText: string) => {
    // Process voice input similar to text input
    if (transcribedText.trim()) {
      try {
        const userMessage: ChatData = {
          id: `${Date.now()}`,
          text: transcribedText,
          userId: session?.user?.email || "user",
          role: "user",
          createdAt: {
            seconds: Math.floor(Date.now() / 1000),
            nanoseconds: 0,
          },
          fileDocumentId,
          category: currentCategory,
          fileName,
        };
  
        await saveUserMessage(userMessage);
        setMessages((prev) => [...prev, userMessage]);
  
        // Get chat history and process response
        const chatHistory = await getChatHistory(session?.user?.email!, selectedDocId!);
        const combinedHistory = chatHistory
          .map((msg) => `${msg.role === "user" ? "User: " : "AI: "}${msg.text}`)
          .join("\n");
  
        const newAbortController = new AbortController();
        setAbortController(newAbortController);
  
        await fetchAIResponse(combinedHistory, transcribedText, "", newAbortController.signal);
      } catch (error) {
        console.error("Error processing voice input:", error);
        setError("Failed to process voice input. Please try again.");
      }
    }
  };
  
  const handleVoiceResponse = (responseText: string) => {
    // This will be called when the AI response should be spoken
    // The VoiceChat component will handle the actual speech synthesis
    //console.log("Speaking response:", responseText);
  };
  

  const fetchAIResponse = async (
    combinedHistory: string,
    userPrompt: string,
    menuSelected: string,
    signal: AbortSignal
  ) => {
    setPageContent('Document preview loading')
    setPageNumber(null)
    setDocTitle('Unknown Document')
    setIsStreaming(true)
    setError(null)
  
    const aiMessageId = `${Date.now()}`
    const aiMessage: ChatData = {
      id: aiMessageId,
      text: '<span class="dot"></span><span class="dot"></span><span class="dot"></span>',
      userId: 'ai-response',
      role: 'ai',
      createdAt: {
        seconds: Math.floor(Date.now() / 1000),
        nanoseconds: 0,
      },
      fileDocumentId,
      stopped: false,
      category: currentCategory,
      fileName: fileName
    }
  
    setStreamingMessageId(aiMessageId)
    setMessages((prevMessages) => [...prevMessages, aiMessage])
  
    let cumulativeStreamedText = ''
  
    try {
      const chatModel = '/api/chatGroq'
      const response = await fetch(chatModel, {
        method: 'POST',
        body: JSON.stringify({ 
          prompt: userPrompt, 
          combinedHistory, 
          fileDocumentId, 
          menuSelected 
        }),
        headers: { 'Content-Type': 'application/json' },
        signal: signal
      })
  
      if (!response.ok) {
        if (response.status === 413) {
          throw new Error('Request too large. Please reduce your message size and try again.')
        } else if (response.status === 404) {
          throw new Error('AI service not found. Please check your connection and try again.')
        }
        throw new Error(`HTTP error! status: ${response.status}`)
      }
  
      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('No reader available')
      }
  
      let metadataReceived = false
  
      while (true) {
        const { done, value } = await reader.read()
        if (done) {
          setStreamingMessageId(null)
          setIsStreaming(false)
          break
        }
  
        const chunk = new TextDecoder().decode(value)
  
        try {
          const parsedChunk = JSON.parse(chunk)
          
          if (!metadataReceived && parsedChunk.type === 'metadata') {
            setPageContent(parsedChunk.content.pageContent || '')
            setPageNumber(parsedChunk.content.pageNumber || null)
            setDocTitle(parsedChunk.content.pageTitle || 'Unknown Document')
            metadataReceived = true
          } else if (parsedChunk.type === 'json') {
            // Handle JSON data (e.g., visualizations)
            const cleanedData = cleanJsonData(parsedChunk.content)
            setMessages((prevMessages) => {
              const updatedMessages = [...prevMessages]
              const aiMessageIndex = updatedMessages.findIndex((msg) => msg.id === aiMessageId)
              if (aiMessageIndex > -1) {
                updatedMessages[aiMessageIndex] = {
                  ...updatedMessages[aiMessageIndex],
                  text: JSON.stringify(cleanedData),
                  stopped: false
                }
              }
              return updatedMessages
            })
          } else if (parsedChunk.type === 'text' || parsedChunk.type === 'markdown') {
            cumulativeStreamedText += parsedChunk.content
            setStreamingText(cumulativeStreamedText)
            setMessages((prevMessages) => {
              const updatedMessages = [...prevMessages]
              const aiMessageIndex = updatedMessages.findIndex((msg) => msg.id === aiMessageId)
              if (aiMessageIndex > -1) {
                updatedMessages[aiMessageIndex] = {
                  ...updatedMessages[aiMessageIndex],
                  text: cumulativeStreamedText,
                  stopped: false
                }
              }
              return updatedMessages
            })
          }
        } catch (error) {
          console.warn('Error parsing chunk:', error)
          // Assume it's plain text if parsing fails
          cumulativeStreamedText += chunk
          setStreamingText(cumulativeStreamedText)
          setMessages((prevMessages) => {
            const updatedMessages = [...prevMessages]
            const aiMessageIndex = updatedMessages.findIndex((msg) => msg.id === aiMessageId)
            if (aiMessageIndex > -1) {
              updatedMessages[aiMessageIndex] = {
                ...updatedMessages[aiMessageIndex],
                text: cumulativeStreamedText,
                stopped: false
              }
            }
            return updatedMessages
          })
        }
      }
  
      setStreamingMessageId(null)
      setIsStreaming(false)
  
      if (!signal.aborted) {
        const finalAiMessage: ChatData = {
          id: aiMessageId,
          text: cumulativeStreamedText,
          userId: 'ai-response',
          role: 'ai',
          createdAt: {
            seconds: Math.floor(Date.now() / 1000),
            nanoseconds: 0,
          },
          fileDocumentId,
          stopped: false,
          category: currentCategory,
          fileName: fileName
        }
  
        await saveAIMessage(finalAiMessage)
  
        if (session?.user?.email && selectedDocId && category) {
          try {
            const lastUserMessage = messages[messages.length - 2]
            
            if (lastUserMessage?.text !== 'Begin Messaging' && lastUserMessage) {
              const vectorId = await saveChatInteraction(
                lastUserMessage,
                finalAiMessage
              )
  
              if (vectorId) {
                //console.log('Successfully saved chat vector with ID:', vectorId)
              }
            }
          } catch (error) {
            console.error('Failed to save chat vector:', error)
          }
        } else {
          //console.log('Skipping vector storage due to missing required data', {
            hasSession: !!session?.user?.email,
            selectedDocId,
            category
          })
        }
      }
  
    } catch (error) {
      console.error('Error fetching AI response:', error)
      
      if (error instanceof DOMException && error.name === 'AbortError') {
        setStreamingText('AI response generation was stopped.')
      } else if (error instanceof Error) {
        setError(error.message)
        setStreamingText('An error occurred while fetching the AI response. Please try again.')
      } else {
        setError('An unknown error occurred while fetching the AI response.')
        setStreamingText('An unknown error occurred. Please try again.')
      }
      
      setStreamingMessageId(null)
    } finally {
      setIsStreaming(false)
    }
  }

  const cleanJsonData = (data: any): any => {
    if (typeof data === 'object' && data !== null) {
      if (Array.isArray(data)) {
        return data.map(cleanJsonData);
      } else {
        const cleanedObject: {[key: string]: any} = {};
        for (const [key, value] of Object.entries(data)) {
          if (value !== undefined && value !== null) {
            cleanedObject[key] = cleanJsonData(value);
          }
        }
        return cleanedObject;
      }
    }
    return data;
  };

  const handleStopProcessing = () => {
    if (abortController) {
      abortController.abort();
      setAbortController(null);
    }
    setIsStreaming(false);
    setIsPending(false);

    setMessages((prev) => {
      const updated = [...prev];
      const lastIdx = updated.length - 1;
      if (lastIdx >= 0 && updated[lastIdx].role === "ai") {
        updated[lastIdx] = {
          ...updated[lastIdx],
          text: "Message generation stopped.",
          stopped: true,
        };
      }
      return updated;
    });
    setStreamingMessageId(null);
    setStreamingText("");
  };

  const getIsLastAIMessage = (index: number) => {
    const aiMessages = messages.filter((m) => m.userId === "ai-response");
    return aiMessages.length > 0 && aiMessages[aiMessages.length - 1] === messages[index];
  };

  const getIsLastUserMessage = (index: number) => {
    const userMessages = messages.filter((m) => m.userId === session?.user?.email);
    return userMessages.length > 0 && userMessages[userMessages.length - 1] === messages[index];
  };

  const handleReprocess = (messageText: string) => {
    //console.log("Reprocessing message:", messageText);
    handleSendMessage(messageText, "");
  };

  const saveChatInteraction = async (userMessage: ChatData, aiMessage: ChatData) => {
    if (!session?.user?.email || !selectedDocId ) {
      //console.log('Missing required data for chat vector storage:', {
        hasSession: !!session?.user?.email,
        selectedDocId,
        category: category || 'Unknown'
      })
      return null
    }

    try {
      const userMessageForVector: ChatMessage = {
        id: userMessage.id,
        text: userMessage.text,
        role: userMessage.role as 'user' | 'ai',
        createdAt: {
          seconds: typeof userMessage.createdAt === 'object' && 'seconds' in userMessage.createdAt
            ? userMessage.createdAt.seconds
            : Math.floor(Date.now() / 1000),
          nanoseconds: typeof userMessage.createdAt === 'object' && 'nanoseconds' in userMessage.createdAt
            ? userMessage.createdAt.nanoseconds
            : 0
        },
        fileDocumentId: userMessage.fileDocumentId ?? '',
        fileName: userMessage.fileName ?? '',
        category: userMessage.category ?? '',
        userId: userMessage.id ?? ''
      }

      const aiMessageForVector: ChatMessage = {
        id: aiMessage.id,
        text: aiMessage.text,
        role: 'ai',
        createdAt: {
          seconds: typeof aiMessage.createdAt === 'object' && 'seconds' in aiMessage.createdAt
            ? aiMessage.createdAt.seconds
            : Math.floor(Date.now() / 1000),
          nanoseconds: typeof aiMessage.createdAt === 'object' && 'nanoseconds' in aiMessage.createdAt
            ? aiMessage.createdAt.nanoseconds
            : 0
        },
        fileDocumentId: aiMessage.fileDocumentId ?? '',
        fileName: aiMessage.fileName ?? '',
        category: aiMessage.category ?? '',
        userId: aiMessage.id ?? ''
      }

      const response = await fetch('/api/chatVector', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: session.user.email,
          category: category || 'Unknown',
          chatId: selectedDocId,
          documentNamespace: selectedDocId,
          userMessage: userMessageForVector,
          aiMessage: aiMessageForVector,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `Failed to save chat interaction: ${response.status}`)
      }

      const data = await response.json()
      //console.log('Successfully saved chat vector:', data)
      return data.vectorId
    } catch (error) {
      console.error('Error saving chat interaction:', error)
      return null
    }
  }


  const resetChat = () => {
    setMessages([]);
    setIsFirstMessage(true);
    setFirstValidUserMessage(null);
    setFirstValidAIResponse(null);
    setStreamingMessageId(null);
    setStreamingText("");
    setError(null);
    setResetWelcome(true); // Added to reset welcome message
  };

  return (
    <div className="flex h-full min-h-0 w-full">
      <div
        className={`flex flex-col min-w-0 w-full overflow-hidden ${
          showPdfArea ? "lg:w-[calc(100%-320px)]" : "w-full"
        }`}
      >
        <div className="flex-1 overflow-y-auto min-h-0 custom-scrollbar">
          <div className="px-4 py-2">
            <InitialResponses
              initialResponses={initialResponses}
              onPromptClick={(prompt) => handleSendMessage(prompt, "")}
              showInitialResponses={true}
              documentName={docTitle}
              isFirstMessage={isFirstMessage}
              firstName={session?.user?.name?.split(" ")[0] || ""}
              resetWelcome={resetWelcome} // Passed resetWelcome prop
            />
          </div>

          {isImageDoc && imageUrl && (
            <ImageDisplay imageUrl={imageUrl} docTitle={docTitle} fileType={fileType} />
          )}

          {error && (
            <div
              className="mb-4 mx-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded relative"
              role="alert"
            >
              <strong className="font-bold">Error!</strong>
              <span className="block sm:inline"> {error}</span>
              <AlertCircle className="absolute right-4 top-4 h-6 w-6 text-red-500" />
            </div>
          )}

          <div className="flex flex-col px-4 min-h-0">
            {loadingMessages && <div className="text-amber-300">Loading messages...</div>}
            {errorMessages && <div>Error loading messages</div>}

            {messages.map((msg, index) => {
              const messageProps = {
                message: {
                  text: msg.text,
                  userId: msg.userId,
                  createdAt: msg.createdAt
                    ? {
                        seconds: msg.createdAt.seconds,
                        nanoseconds: msg.createdAt.nanoseconds,
                      }
                    : undefined
                },
                session: {
                  user: {
                    email: session?.user?.email || "<EMAIL>",
                    image: session?.user?.image || null,
                  },
                },
                isStreaming: msg.id === streamingMessageId,
                streamedData: msg.id === streamingMessageId ? streamingText || msg.text : msg.text,
                isLastAIMessage: getIsLastAIMessage(index),
                isLastUserMessage: getIsLastUserMessage(index),
                hasAIResponded: false,
                onFollowUpClick: (question: string) => handleSendMessage(question, ""),
                onListItemClick: handleListItemClick,
                onReprocess: handleReprocess,
                visualizationUrl: msg.visualizationUrl,
              };

              return <Message key={msg.id} {...messageProps} />;
            })}
            <div ref={messagesEndRef} />
          </div>
        </div>

        <div className="flex-none px-4 py-1 bg-ike-message-bg">
        {isVoiceActive && (
          <div className="mb-4">
            <VoiceChat
              onVoiceInput={handleVoiceInput}
              onVoiceResponse={handleVoiceResponse}
              isProcessing={isPending}
            />
          </div>
        )}          
          <MessageInput
            onSendMessage={(messageText: string) => handleSendMessage(messageText, "")}
            onStopProcessing={handleStopProcessing}
            isPending={isPending}
            
            isFirstMessage={isFirstMessage}
            onResetChat={resetChat}
            onToggleVoice={handleToggleVoice}
            isVoiceActive={isVoiceActive}
          />
        </div>
      </div>

      <div className="absolute top-1/2 right-0 transform -translate-y-1/2 z-50">
        <button
          onClick={() => setShowPdfArea(!showPdfArea)}
          className="flex items-center gap-2 px-1 py-2 text-sm font-medium opacity-30 shadow-md shadow-black text-amber-500 bg-ike-dark-purple rounded-lg hover:bg-opacity-80"
        >
          {showPdfArea ? ">> Hide" : "<< Show"} Sources
        </button>
      </div>

      {showPdfArea && (
        <div className="hidden lg:flex w-80 flex-none flex-col min-h-0 bg-ike-dark-purple">
          <div className="flex-1 overflow-hidden min-h-0">
            <PdfDocumentArea
              pageContent={pageContent.trim()}
              pageNumber={pageNumber?.slice(0, -2) || null}
              docTitle={docTitle}
              onPageContentUpdate={setPageContent}
              onPageNumberUpdate={setPageNumber}
            />
          </div>
        </div>
      )}

      <style jsx global>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 8px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: transparent;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background-color: rgba(255, 255, 255, 0.2);
          border-radius: 20px;
          border: transparent;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background-color: rgba(255, 255, 255, 0.3);
        }
      `}</style>
    </div>
  );
}