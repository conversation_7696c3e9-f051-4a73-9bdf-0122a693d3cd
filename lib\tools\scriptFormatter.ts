import { z } from 'zod';
import { <PERSON><PERSON><PERSON><PERSON>ider, llmTool } from './llm-tool'; // Assuming llm-tool.ts is in the same directory

// --- Constants ---
const DEFAULT_MODEL = "gemini-1.5-pro-latest"; // Using latest stable generally good
const DEFAULT_PDF_MODEL = "gemini-1.5-pro-latest"; // PDFs might benefit from larger context models
const MAX_LLM_PROMPT_LENGTH = 30000; // Max characters for script content to send to LLM for non-PDF
const MAX_PDF_LLM_PROMPT_LENGTH = 100000; // PDFs can be very verbose, allow more if model supports
const LLM_MAX_TOKENS = 8000; // Increased, ensure model supports this (e.g., Gemini 1.5 Pro)
const LLM_TEMPERATURE = 0.1; // Low temperature for factual extraction
const FALLBACK_MAX_LINES_DISPLAY = 75; // Max lines to show in fallback

// --- Schemas (unchanged, but good) ---
const ScriptMetadataSchema = z.object({
  title: z.string().describe("The title of the script. Infer if not explicitly stated."),
  author: z.string().describe("The author or playwright of the script. Infer if not explicitly stated."),
  characters: z.array(z.string()).describe("List of ALL unique character names found in the script (normalized to uppercase, e.g., ['WILLY', 'BIFF'])."),
  summary: z.string().describe("A brief summary of the script, its main plot points, and its setting."),
});

const ScriptLineSchema = z.object({
  lineNumber: z.number().int().positive().describe("The sequential line number in the script, starting from 1."),
  character: z.string().describe("The character speaking this line. This field must contain *only* the character's name (e.g., 'WILLY', 'BIFF'), normalized to uppercase. If the speaker is ambiguous or a narrator, use 'NARRATOR' or infer from context."),
  text: z.string().describe("The text/dialogue of this line. This field must contain *only* the actual spoken words of the character. Do not include the character's name or any prefixes (e.g., 'Character Name:') in this field."),
  notes: z.string().optional().describe("Optional stage directions, actions, parentheticals, or notes about this line (e.g., '(angrily)', 'exits stage left', 'to herself')."),
});

const FormattedScriptSchema = z.object({
  metadata: ScriptMetadataSchema,
  lines: z.array(ScriptLineSchema),
});

export type ScriptMetadata = z.infer<typeof ScriptMetadataSchema>;
export type ScriptLine = z.infer<typeof ScriptLineSchema>;
export type FormattedScript = z.infer<typeof FormattedScriptSchema>;

export interface ScriptFormattingOptions {
  model?: string;
  provider?: LlmProvider;
  // includeLineNumbers and includeNotes are now handled by the schema and prompt directly.
  // The schema defines what's expected. The prompt instructs the LLM to fill it.
}

export class ScriptFormatterTool {
  static description = {
    name: "scriptFormatter",
    description: "Formats raw script text (including from PDF extractions) into a structured JSON object with metadata, character lists, and dialogue lines for rehearsal.",
    schema: {
      formatScript: {
        description: "Analyzes raw script content, extracts metadata, characters, and dialogue lines, and returns a structured JSON object.",
        parameters: {
          scriptContent: "The full raw script content as a string. This can be plain text or text extracted from a PDF.",
          options: "Optional: { model?: string, provider?: LlmProvider }"
        },
        returns: "A JSON object conforming to FormattedScriptSchema, containing script metadata and an array of structured script lines."
      }
    }
  };

  async formatScript(
    scriptContent: string,
    options: ScriptFormattingOptions = {}
  ): Promise<FormattedScript> {
    if (!scriptContent || scriptContent.trim().length === 0) {
      return this._createMinimalFallback("Empty script content provided.");
    }

    const isProbablyPdf = this._isProbablyPdfContent(scriptContent);
    const effectiveModel = options.model || (isProbablyPdf ? DEFAULT_PDF_MODEL : DEFAULT_MODEL);
    const provider = options.provider || "google";

    try {
      const systemPrompt = this._createSystemPrompt(isProbablyPdf);
      const truncatedScriptContent = scriptContent.substring(0, isProbablyPdf ? MAX_PDF_LLM_PROMPT_LENGTH : MAX_LLM_PROMPT_LENGTH);

      if (truncatedScriptContent.length < scriptContent.length) {
          console.warn(`Script content was truncated from ${scriptContent.length} to ${truncatedScriptContent.length} characters for LLM processing.`);
      }

      let llmResponseText: string;
      try {
        llmResponseText = await llmTool.processContent({
          prompt: truncatedScriptContent,
          context: systemPrompt,
          model: effectiveModel,
          provider,
          modelOptions: {
            temperature: LLM_TEMPERATURE,
            maxTokens: LLM_MAX_TOKENS,
            // Attempt to force JSON output if the provider/model supports it via llmTool
            // This is often model-specific (e.g., Gemini's `response_mime_type` or OpenAI's JSON mode)
            // Assuming llmTool might handle this if a 'responseFormat' or similar option is available.
            // response_format: { type: "json_object" }, // Example for OpenAI
            // response_mime_type: "application/json", // Example for Gemini
          }
        });

        if (!llmResponseText || llmResponseText.trim() === "") {
            throw new Error("LLM returned an empty response.");
        }
         // Basic check for common LLM error messages, but parsing will be the main validation
        if (llmResponseText.length < 50 && (llmResponseText.toLowerCase().includes("error") || llmResponseText.toLowerCase().includes("failed"))) {
            throw new Error("LLM processing error: " + llmResponseText.substring(0, 200));
        }

      } catch (llmError) {
        console.error("Error during LLM processing:", llmError);
        const errorMessage = llmError instanceof Error ? llmError.message : String(llmError);
        // Fallback to rule-based parsing on the original content if LLM fails
        return this._createFallbackScript(
            `LLM processing failed: ${errorMessage}. Trying rule-based fallback.`,
            scriptContent,
            undefined // No LLM response to pass
        );
      }

      return this._parseLlmResponse(llmResponseText, scriptContent);

    } catch (error: any) {
      console.error("Critical error in formatScript:", error);
      // Ensure the error thrown here is also a new Error object if not already
      const errorMessage = error instanceof Error ? error.message : String(error.message || error);
      // Final fallback to rule-based parsing or minimal if that also fails
      try {
        return this._createFallbackScript(`Script formatting failed: ${errorMessage}. Trying rule-based fallback.`, scriptContent);
      } catch (fallbackError) {
        return this._createMinimalFallback(`Primary formatting and fallback failed: ${errorMessage}`);
      }
    }
  }

  private _createSystemPrompt(isPdfContent: boolean): string {
    const schemaDescription = this._generateSchemaDescription(); // JSON string of the schema

    // Common instructions
    let prompt = "You are an expert script formatting AI. Your task is to analyze the provided script content and convert it into a structured JSON object.\n" +
      "The output MUST strictly conform to the following JSON schema:\n" +
      `${schemaDescription}\n\n` +
      "KEY REQUIREMENTS:\n" +
      "1.  **METADATA**: Extract or accurately infer the 'title', 'author'. List ALL unique 'characters' (normalized to UPPERCASE). Provide a concise 'summary'.\n" +
      "2.  **LINES**: For each dialogue line:\n" +
      "    a.  `lineNumber`: Assign a sequential positive integer, starting from 1.\n" +
      "    b.  `character`: Provide ONLY the character's name (UPPERCASE). If ambiguous, use 'NARRATOR' or your best inference. Do NOT include dialogue here.\n" +
      "    c.  `text`: Provide ONLY the spoken dialogue. Do NOT include character names, colons, or stage directions here.\n" +
      "    d.  `notes`: Include ALL stage directions, parentheticals, actions, or delivery instructions relevant to this line or character. If none, this field can be omitted or empty.\n" +
      "3.  **ACCURACY**: Meticulously separate character names, dialogue, and stage directions into their respective fields.\n" +
      "4.  **NORMALIZATION**: Character names MUST be normalized to UPPERCASE (e.g., 'WILLY', not 'Willy:').\n" +
      "5.  **COMPLETENESS**: Process the entire script and extract all dialogue lines.\n" +
      "6.  **JSON VALIDITY**: The entire response MUST be a single, valid JSON object. Do not include any text, explanations, or markdown formatting (like ```json) outside of this JSON object.\n\n";

    // Content-specific instructions
    if (isPdfContent) {
      prompt += "PDF/SCANNED CONTENT GUIDANCE:\n" +
        "- The input is likely from a PDF or scanned document and may contain formatting errors, fragmented text, page numbers, or OCR artifacts.\n" +
        "- Apply your understanding of script structure to intelligently reconstruct the script. Ignore irrelevant artifacts (page numbers, headers/footers unless they are part of the script title/author).\n" +
        "- Dialogue might be split across lines or mixed with character names due to OCR. Reassemble it correctly.\n" +
        "- Be very careful to distinguish character names from dialogue, even if the formatting is poor.\n\n";
    } else {
      prompt += "STANDARD SCRIPT GUIDANCE:\n" +
        "- Handle various common script formats (e.g., 'CHARACTER: Dialogue', screenplays, stage plays).\n" +
        "- Correctly parse character names, dialogue, and stage directions (often in parentheses or on separate lines).\n\n";
    }

    // Examples to guide the LLM
    prompt += "PROCESSING EXAMPLES:\n" +
      "Input: 'WILLY: You're a dime a dozen! (Angrily)'\n" +
      "Output Line: { \"lineNumber\": 123, \"character\": \"WILLY\", \"text\": \"You're a dime a dozen!\", \"notes\": \"(Angrily)\" }\n\n" +
      "Input: 'Ophelia\n(Singing)\nTomorrow is Saint Valentine's day.'\n" +
      "Output Line: { \"lineNumber\": 124, \"character\": \"OPHELIA\", \"text\": \"Tomorrow is Saint Valentine's day.\", \"notes\": \"(Singing)\" }\n\n" +
      "Input: 'BIFF (to Happy): He's not going to listen.'\n" +
      "Output Line: { \"lineNumber\": 125, \"character\": \"BIFF\", \"text\": \"He's not going to listen.\", \"notes\": \"(to Happy)\" }\n\n" +
      "Remember, your entire response must be ONLY the JSON object. ";

    return prompt;
  }

  private _generateSchemaDescription(): string {
    // This method is fine, but for complex schemas, using a library like zod-to-json-schema
    // might be more robust if the LLM needs a very precise JSON Schema definition.
    // For current LLMs, providing the Zod descriptions directly in the prompt (as done)
    // and a simplified structure like this is often effective.
    const schemaForLLM = {
      type: "object",
      properties: {
        metadata: {
          type: "object",
          properties: {
            title: { type: "string", description: ScriptMetadataSchema.shape.title.description },
            author: { type: "string", description: ScriptMetadataSchema.shape.author.description },
            characters: {
              type: "array",
              items: { type: "string" },
              description: ScriptMetadataSchema.shape.characters.description
            },
            summary: { type: "string", description: ScriptMetadataSchema.shape.summary.description }
          },
          required: ["title", "author", "characters", "summary"]
        },
        lines: {
          type: "array",
          items: {
            type: "object",
            properties: {
              lineNumber: { type: "number", description: ScriptLineSchema.shape.lineNumber.description },
              character: { type: "string", description: ScriptLineSchema.shape.character.description },
              text: { type: "string", description: ScriptLineSchema.shape.text.description },
              notes: { type: "string", description: ScriptLineSchema.shape.notes.description + " (This field is optional)" }
            },
            required: ["lineNumber", "character", "text"] // notes is optional
          }
        }
      },
      required: ["metadata", "lines"]
    };
    return JSON.stringify(schemaForLLM, null, 2);
  }

  private _extractJsonFromString(str: string): string | null {
    // Try to find JSON block within ```json ... ```
    const jsonBlockMatch = str.match(/```(?:json)?\s*([\s\S]+?)\s*```/);
    if (jsonBlockMatch && jsonBlockMatch[1]) {
      return jsonBlockMatch[1].trim();
    }

    // If not found, try to find the first '{' and last '}'
    // This is less reliable but a common fallback.
    let firstBrace = -1;
    let lastBrace = -1;
    let openBraceCount = 0;

    for (let i = 0; i < str.length; i++) {
        if (str[i] === '{') {
            if (openBraceCount === 0) {
                firstBrace = i;
            }
            openBraceCount++;
        } else if (str[i] === '}') {
            openBraceCount--;
            if (openBraceCount === 0 && firstBrace !== -1) {
                lastBrace = i;
                // Consider this a complete JSON object if we found a balanced set
                // For simplicity, we'll just use the outermost first/last brace found overall
                // and let JSON.parse validate. A more complex parser could be used here.
            }
        }
    }
    // Update lastBrace to be the actual last brace in the string if firstBrace was found
    if (firstBrace !== -1) {
        const trueLastBrace = str.lastIndexOf('}');
        if (trueLastBrace > firstBrace) {
            lastBrace = trueLastBrace;
        }
    }


    if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
      return str.substring(firstBrace, lastBrace + 1).trim();
    }
    
    // If the string itself starts with { and ends with }, assume it's all JSON
    if (str.trim().startsWith('{') && str.trim().endsWith('}')) {
        return str.trim();
    }

    return null; // No clear JSON found
  }

  private _parseLlmResponse(llmResponseText: string, originalScriptContent: string): FormattedScript {
    const jsonContent = this._extractJsonFromString(llmResponseText);

    if (!jsonContent) {
      return this._createFallbackScript("Could not extract JSON from LLM response.", originalScriptContent, llmResponseText);
    }

    try {
      const parsedJson = JSON.parse(jsonContent);
      const validationResult = FormattedScriptSchema.safeParse(parsedJson);

      if (!validationResult.success) {
        const errorDetails = validationResult.error.errors.map(err =>
          `${err.path.join('.') || 'root'}: ${err.message}`
        ).join('; ');
        console.warn("LLM response JSON failed schema validation:", errorDetails, "\nLLM JSON Content:\n", jsonContent.substring(0,500) + "...");
        // Even if schema validation fails, try to use the LLM's structured attempt in the fallback
        // This is better than just raw text if the LLM got *some* structure right.
        return this._createFallbackScript(
            `LLM JSON schema validation failed: ${errorDetails}`,
            originalScriptContent,
            jsonContent // Pass the potentially malformed JSON to fallback
        );
      }
      console.log("LLM response parsed and validated successfully.");
      // Perform a light post-processing to ensure character names are uppercase
      // and line numbers are sequential if the LLM messed them up.
      validationResult.data.lines.forEach((line, index) => {
        line.character = line.character.toUpperCase();
        line.lineNumber = index + 1; // Enforce sequential line numbers
        if (line.notes === null || typeof line.notes === 'undefined') {
            delete line.notes; // Ensure optional field is truly absent if null/undefined
        }
      });
       validationResult.data.metadata.characters = [
        ...new Set(validationResult.data.metadata.characters.map(c => c.toUpperCase()))
       ].sort();


      return validationResult.data;

    } catch (parseError) {
      const errorMessage = parseError instanceof Error ? parseError.message : String(parseError);
      console.error("JSON parsing failed for extracted content:", errorMessage, "\nExtracted Content:\n", jsonContent.substring(0, 500) + "...");
      return this._createFallbackScript(`JSON parsing error: ${errorMessage}`, originalScriptContent, llmResponseText);
    }
  }

  /**
   * Enhanced fallback script creator that attempts rule-based parsing.
   */
  private _createFallbackScript(failureReason: string, originalContent: string, llmAttempt?: string): FormattedScript {
    console.warn("Creating fallback script due to:", failureReason);

    // Prefer to parse llmAttempt if it's somewhat structured, otherwise use originalContent
    const contentToParse = llmAttempt || originalContent;
    const lines = contentToParse.split('\n');

    let title = "Script (Fallback)";
    let author = "Unknown (Fallback)";
    const characters = new Set<string>();
    let summary = `Fallback generated: ${failureReason}. Displaying rule-based parsing attempt.`;

    const parsedLines: ScriptLine[] = [];
    let currentLineNumber = 1;

    // Try to parse metadata from llmAttempt if it's JSON-like
    if (llmAttempt) {
        try {
            const partialParse = JSON.parse(llmAttempt); // May throw, caught by outer try/catch
            if (partialParse.metadata) {
                title = partialParse.metadata.title || title;
                author = partialParse.metadata.author || author;
                if (Array.isArray(partialParse.metadata.characters)) {
                    partialParse.metadata.characters.forEach((c:any) => typeof c === 'string' && characters.add(c.toUpperCase()));
                }
                summary = partialParse.metadata.summary || summary;
            }
            // If lines exist in llmAttempt, try to use them as a base
            if (Array.isArray(partialParse.lines)) {
                 partialParse.lines.slice(0, FALLBACK_MAX_LINES_DISPLAY).forEach((line: any) => {
                    if (line && typeof line.character === 'string' && typeof line.text === 'string') {
                        const char = line.character.trim().toUpperCase() || "UNKNOWN";
                        characters.add(char);
                        parsedLines.push({
                            lineNumber: currentLineNumber++,
                            character: char,
                            text: String(line.text).trim(),
                            notes: typeof line.notes === 'string' ? String(line.notes).trim() : undefined
                        });
                    }
                });
                if (parsedLines.length > 0) {
                     // If we got lines from the LLM attempt, we might be done with line parsing here.
                     // Add a note about the truncation.
                    if (partialParse.lines.length > FALLBACK_MAX_LINES_DISPLAY) {
                         parsedLines.push({
                            lineNumber: currentLineNumber++,
                            character: "SYSTEM NOTE",
                            text: `... ${partialParse.lines.length - FALLBACK_MAX_LINES_DISPLAY} more lines from LLM attempt truncated ...`,
                            notes: "Content limited in fallback."
                        });
                    }
                    // Skip rule-based line parsing if LLM provided some lines
                    return {
                        metadata: { title, author, characters: Array.from(characters).sort(), summary },
                        lines: parsedLines
                    };
                }
            }
        } catch (e) {
            console.warn("Could not parse llmAttempt as JSON during fallback:", (e as Error).message);
        }
    }


    // If no lines from LLM attempt, or llmAttempt was not helpful, proceed with rule-based parsing of originalContent
    parsedLines.push({ // Add the failure reason as the first line
        lineNumber: currentLineNumber++,
        character: "SYSTEM",
        text: `Formatting Error: ${failureReason}. The following is a rule-based attempt.`,
        notes: "This is an automatically generated message."
    });

    const characterLineRegex = /^([A-Z\s0-9()'-]+):?\s*(.*)/; // Character (often all caps): Dialogue
    const stageDirectionRegex = /^\s*(\([\s\S]+\)|\[[\s\S]+\])\s*$/; // (Stage direction) or [Stage direction]
    const titleRegex = /^(?:title:\s*)(.+)/i;
    const authorRegex = /^(?:author:|by\s+)(.+)/i;

    let lastCharacter = "NARRATOR";

    for (const line of originalContent.split('\n')) {
        if (currentLineNumber > FALLBACK_MAX_LINES_DISPLAY + 1) break; // +1 for the system message

        const trimmedLine = line.trim();
        if (!trimmedLine) continue;

        // Metadata extraction (simple)
        if (currentLineNumber < 10) { // Only check first few lines for title/author
            const titleMatch = trimmedLine.match(titleRegex);
            if (titleMatch) { title = titleMatch[1].trim(); continue; }
            const authorMatch = trimmedLine.match(authorRegex);
            if (authorMatch) { author = authorMatch[1].trim(); continue; }
            if (currentLineNumber === 1 && trimmedLine.length < 80 && !trimmedLine.includes(':')) {
                 // First non-blank line might be a title if not dialogue-like
                title = trimmedLine;
                continue;
            }
        }


        const charMatch = trimmedLine.match(characterLineRegex);
        if (charMatch) {
            const potentialChar = charMatch[1].trim().replace(/\(\s*\w+\s*\)$/, '').trim(); // Remove trailing (CONT'D)
            const dialogueText = charMatch[2].trim();
            const notesMatch = dialogueText.match(/^(\([\s\S]+\)|\[[\s\S]+\])\s*(.*)/); // Note at start of dialogue

            let actualDialogue = dialogueText;
            let notes: string | undefined = undefined;

            if (notesMatch) {
                notes = notesMatch[1];
                actualDialogue = notesMatch[2].trim();
            }

            // Further check for notes embedded or at the end
            const endNotesMatch = actualDialogue.match(/(.*?)(\([\s\S]+\)|\[[\s\S]+\])$/);
            if (endNotesMatch) {
                actualDialogue = endNotesMatch[1].trim();
                notes = notes ? `${notes} ${endNotesMatch[2]}` : endNotesMatch[2];
            }


            if (potentialChar.length > 0 && potentialChar.length < 30) { // Heuristic for valid char name length
                lastCharacter = potentialChar.toUpperCase();
                characters.add(lastCharacter);
                 if (actualDialogue) {
                    parsedLines.push({
                        lineNumber: currentLineNumber++,
                        character: lastCharacter,
                        text: actualDialogue,
                        notes: notes?.trim()
                    });
                } else if (notes) { // Character line with only a stage direction
                     parsedLines.push({
                        lineNumber: currentLineNumber++,
                        character: lastCharacter,
                        text: "", // No dialogue, just notes
                        notes: notes?.trim()
                    });
                }
                continue;
            }
        }

        const stageMatch = trimmedLine.match(stageDirectionRegex);
        if (stageMatch) {
            // If previous line was dialogue, attach as note, else new line with last character or narrator
            const prevLine = parsedLines[parsedLines.length - 1];
            if (prevLine && !prevLine.notes && prevLine.character !== "SYSTEM") {
                prevLine.notes = (prevLine.notes ? prevLine.notes + " " : "") + stageMatch[1];
            } else {
                parsedLines.push({
                    lineNumber: currentLineNumber++,
                    character: lastCharacter, // Assign to last speaker or NARRATOR
                    text: "", // No dialogue, just notes
                    notes: stageMatch[1]
                });
            }
            continue;
        }

        // If it's not a character line or stage direction, assume it's dialogue for the last character or narrator
        // or a continuation of previous line's text if it was short
        const prevLine = parsedLines[parsedLines.length -1];
        if (prevLine && prevLine.character !== "SYSTEM" && prevLine.text.length < 100 && !trimmedLine.toUpperCase().match(/^[A-Z\s0-9()'-]+:?/)) {
            prevLine.text += " " + trimmedLine; // Append to previous line's text
        } else {
            parsedLines.push({
                lineNumber: currentLineNumber++,
                character: lastCharacter, // Could be NARRATOR if no char identified yet
                text: trimmedLine,
                notes: undefined
            });
        }
    }

    if (originalContent.split('\n').length > FALLBACK_MAX_LINES_DISPLAY) {
      parsedLines.push({
        lineNumber: currentLineNumber++,
        character: "SYSTEM NOTE",
        text: `... ${originalContent.split('\n').length - FALLBACK_MAX_LINES_DISPLAY} more lines from original content truncated in this fallback view ...`,
        notes: ""
      });
    }

    return {
      metadata: {
        title,
        author,
        characters: Array.from(characters).sort(),
        summary,
      },
      lines: parsedLines,
    };
  }

  private _createMinimalFallback(errorMessage: string): FormattedScript {
    console.warn("Creating MINIMAL fallback due to critical error:", errorMessage);
    return {
      metadata: {
        title: "Error Processing Script",
        author: "Unknown",
        characters: ["UNKNOWN"],
        summary: `Critical error during script processing: ${errorMessage}. No content could be meaningfully parsed.`
      },
      lines: [{
        lineNumber: 1,
        character: "SYSTEM",
        text: `Script processing failed: ${errorMessage}. Unable to display content.`,
        notes: "Please check the input script or tool configuration."
      }]
    };
  }

  private _isProbablyPdfContent(content: string): boolean {
    if (content.length < 10) return false;
    if (content.substring(0, 8).startsWith('%PDF')) {
      return true;
    }
    // A more aggressive check for binary-like content often found in malformed PDF text extractions
    // or if raw PDF bytes are accidentally passed as string.
    let nonPrintableOrOddChars = 0;
    const sampleLength = Math.min(content.length, 2048);
    for (let i = 0; i < sampleLength; i++) {
      const charCode = content.charCodeAt(i);
      // Check for common control characters (excluding tab, LF, CR)
      // or characters outside typical Latin-1 extended + common symbols range,
      // or sequences of very unusual characters.
      if ((charCode < 32 && ![9, 10, 13].includes(charCode)) || charCode > 255 ) { // Simplified: looking for non-ASCII/Latin1 or control chars
        nonPrintableOrOddChars++;
      }
    }
    // If more than 5% of a sample are "weird" characters, flag it.
    // This threshold might need tuning.
    return (nonPrintableOrOddChars / sampleLength) > 0.05;
  }

  getDescription(): typeof ScriptFormatterTool.description {
    return ScriptFormatterTool.description;
  }

  getAvailableMethods(): Record<string, string> {
    return {
      formatScript: ScriptFormatterTool.description.schema.formatScript.description
    };
  }
}

export const scriptFormatterTool = new ScriptFormatterTool();